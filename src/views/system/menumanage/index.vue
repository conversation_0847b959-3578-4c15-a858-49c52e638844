<template>
  <div class="p-0.5">
    <avue-crud
      ref="menuCrud"
      v-model="menuForm"
      :data="menuData"
      :option="option"
      :before-open="beforeFormOpenHandler"
      @tree-load="treeLoadHandler"
      @row-save="rowSave"
      @row-update="rowUpdate"
      @row-del="rowDel"
      @refresh-change="reloadMenuData"
      @selection-change="selectionChangeHandler"
    >
      <template #resourceIcon="{ row }">
        <el-button v-if="row.resourceIcon.length > 0" @click="selectIcon(row)">
          <IconifyIconOffline :icon="row.resourceIcon" width="18" />
        </el-button>
        <div v-else class="flex justify-center items-center">
          <IconifyIconOffline icon="RI-MenuLine" width="18" />
        </div>
      </template>
      <template #resourceName="{ row }">
        <span
          v-html="highLightText(row.resourceName, keyWord)"
          class="text-primary font-bold"
        />
        <el-button
          v-if="row.hasChildren"
          type="primary"
          text
          :icon="useRenderIcon('RI-ArrowUpDownLine')"
          @click="sortMenuHandler(row)"
        ></el-button>
      </template>
      <template #resourceCode="{ row }">
        <span v-html="highLightText(row.resourceCode, keyWord)" />
      </template>
      <template #resourceName-form="{ disabled }">
        <el-input
          v-model="menuForm.resourceName"
          :disabled="disabled"
          :maxlength="32"
          :show-word-limit="true"
          clearable
        >
          <template #prepend>
            <el-button
              type="primary"
              :disabled="disabled"
              @click="iconDrawerVisible = true"
            >
              <IconifyIconOffline :icon="menuForm.resourceIcon" width="14" />
            </el-button>
          </template>
        </el-input>
      </template>
      <template #menu="{ row, size, type }">
        <el-button
          :size="size"
          :type="type"
          text
          :icon="useRenderIcon('EP-Plus')"
          :disabled="row.permCode!.length > 0"
          @click="addSubMenu(row)"
        >
          新增
        </el-button>
        <el-button
          :size="size"
          :type="type"
          text
          :icon="useRenderIcon('EP-Promotion')"
          :disabled="row.permCode!.length > 0"
          @click="moveNodeHandler(row)"
        >
          移动
        </el-button>
      </template>
      <template #menu-left="{ size }">
        <div class="flex items-center float-right space-x-3 ml-3 pt-0.5">
          <el-button
            type="primary"
            plain
            :size="size"
            :icon="
              isExpand
                ? useRenderIcon('EP-CaretBottom')
                : useRenderIcon('EP-CaretRight')
            "
            @click="handleExpand"
          >
            {{ isExpand ? "折叠" : "展开" }}
          </el-button>
          <el-tooltip
            class="item"
            effect="light"
            content="提示：折叠/展开功能 仅对已加载的数据有效！"
            placement="right"
            :open-delay="500"
          >
            <iconify-icon-offline icon="EP-InfoFilled" />
          </el-tooltip>
        </div>
      </template>
      <template #menu-right="{ size }">
        <div class="float-left pr-3">
          <el-input
            clearable
            placeholder="菜单名称或标识"
            v-model="keyWord"
            :size="size"
            :suffix-icon="useRenderIcon('EP-Search')"
            @blur="keyWord = ($event.target as HTMLInputElement).value.trim()"
          />
        </div>
        <el-tooltip content="导出菜单数据" placement="top" :open-delay="1000">
          <el-button
            :icon="useRenderIcon('EP-Download')"
            circle
            :size="size"
            :disabled="selectedMenus.length < 1"
            v-auth="AdminEnum.SuperAdmin"
            @click="exportHandler"
          />
        </el-tooltip>
        <el-tooltip content="导入菜单数据" placement="top" :open-delay="1000">
          <el-button
            :icon="useRenderIcon('EP-Upload')"
            circle
            :size="size"
            v-auth="AdminEnum.SuperAdmin"
            @click="importDialogVisible = true"
          />
        </el-tooltip>
        <el-tooltip content="排序首层菜单" placement="top" :open-delay="1000">
          <el-button
            :icon="useRenderIcon('RI-ArrowUpDownLine')"
            circle
            :size="size"
            @click="
              sortMenuHandler({
                resourceId: '-1',
                resourceName: '首层菜单'
              } as ManageResource)
            "
          />
        </el-tooltip>
      </template>
    </avue-crud>

    <!-- 菜单图标选择抽屉 -->
    <icon-selector
      v-model:visible="iconDrawerVisible"
      @select="iconSelectHandler"
    />

    <!-- 菜单数据导入对话框 -->
    <upload-menu-data
      v-model:visible="importDialogVisible"
      @import-success="importSuccessHandler"
    />

    <!-- 移动菜单节点 -->
    <move-menu-node
      v-model:visible="moveNodeVisible"
      :node="selectedMenu"
      @move-success="moveMenuSuccessHandler"
    />

    <!-- 菜单排序 -->
    <menu-sort-drawer
      v-model:visible="sortDrawerVisible"
      :parent-menu="selectedMenu"
      @success="sortSuccessHandler"
    />
  </div>
</template>

<script lang="ts" setup>
import { getCurrentInstance, nextTick, reactive, ref, toRefs } from "vue";
import IconSelector from "@/components/IconSelector/IconSelector.vue";
import { IconifyIconOffline } from "@/components/ReIcon";
import { ResultStatus } from "@/utils/http/types";
import { ElMessageBox, ElNotification } from "element-plus";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";
import { validSignId } from "@/utils/validator";
import { message } from "@/utils/message";
import UploadMenuData from "@/views/system/menumanage/components/UploadMenuData.vue";
import MoveMenuNode from "@/views/system/menumanage/components/MoveMenuNode.vue";
import MenuSortDrawer from "@/views/system/menumanage/components/MenuSortDrawer.vue";
import { highLightText } from "@/utils/view";
import { hasAuth } from "@/router/utils";
import { AdminEnum } from "@/utils/CommonTypes";
import {
  addMenu,
  deleteMenu,
  exportMenuData,
  getMenuData,
  ManageResource,
  updateMenu
} from "@/views/system/menumanage/api/MenuManageApi";

defineOptions({
  name: "SystemConfig_MenuManage"
});

const defaultMenuIcon = "EP-Menu";
const rootParentId = "-1";
const menuCrud = ref();
const option = ref(null);
const menuForm = ref({ resourceIcon: defaultMenuIcon } as ManageResource);

const { $confirm } = getCurrentInstance().appContext.config.globalProperties;

//菜单编码校验
const validateResourceCode = (rule, value, callback) => {
  if (validSignId(value)) {
    callback();
  } else {
    callback(new Error("格式错误,示例：system_manage"));
  }
};

//校验权限编码
const permCodeExp = /^[a-z]+(:[a-z]+)*$/;
const validatePermCode = (rule, value, callback) => {
  if (menuForm.value.resourceType == "OPERATION" && !value) {
    callback(new Error("请填写权限标识"));
  } else if (menuForm.value.resourceType == "OPERATION" && value) {
    if (permCodeExp.test(value)) {
      callback();
    } else {
      callback(new Error("格式错误,示例：system:menu:add"));
    }
  } else {
    callback();
  }
};

//菜单数据Crud配置项
option.value = {
  index: true,
  border: true,
  rowKey: "resourceId",
  rowParentKey: "parentId",
  selection: true,
  lazy: true,
  menuWidth: 235,
  menu: hasAuth([AdminEnum.SuperAdmin, AdminEnum.SystemAdmin]),
  addBtn: hasAuth([AdminEnum.SuperAdmin, AdminEnum.SystemAdmin]),
  gridBtn: false,
  column: [
    {
      label: "名称",
      prop: "resourceName",
      rules: [
        {
          required: true,
          message: "请输入菜单名称",
          trigger: "blur"
        }
      ]
    },
    {
      label: "图标",
      prop: "resourceIcon",
      width: 60,
      editDisplay: false,
      addDisplay: false
    },
    {
      label: "资源类型",
      prop: "resourceType",
      hide: true,
      type: "radio",
      button: true,
      dicData: [
        { label: "系统菜单", value: "MENU" },
        { label: "操作权限", value: "OPERATION" }
      ]
    },
    {
      label: "标识",
      prop: "resourceCode",
      maxlength: 100,
      showWordLimit: true,
      editDisabled: true,
      span: 24,
      rules: [
        {
          required: true,
          message: "请输入菜单标识",
          trigger: "blur"
        },
        { validator: validateResourceCode, trigger: "blur" }
      ]
    },
    {
      label: "路径",
      prop: "resourceUrl",
      maxlength: 400,
      showWordLimit: true,
      span: 24
    },
    {
      label: "权限标识",
      prop: "permCode",
      maxlength: 64,
      showWordLimit: true,
      editDisabled: true,
      rules: [{ validator: validatePermCode, trigger: "blur" }]
    },
    {
      label: "资源分类",
      prop: "resourceTypeCode",
      hide: true,
      type: "radio",
      button: true,
      dicData: [
        { label: "PC", value: "PC" },
        { label: "APP", value: "APP" }
      ]
    },
    {
      label: "排序标识",
      prop: "sortIndex",
      type: "number",
      min: 0,
      max: 999,
      width: 70
    },
    {
      label: "打开方式",
      prop: "openTarget",
      hide: true,
      type: "radio",
      button: true,
      dicData: [
        { label: "Tab页", value: "Tab" },
        { label: "新窗口", value: "NewWindow" }
      ]
    },
    {
      label: "隐藏",
      prop: "isHidden",
      type: "switch",
      width: 60,
      hide: true
    },
    {
      label: "描述",
      prop: "resourceInfo",
      hide: true,
      type: "textarea",
      maxlength: 200,
      span: 24,
      showWordLimit: true
    }
  ]
};

//定义Data
const state = reactive({
  iconDrawerVisible: false,
  importDialogVisible: false,
  moveNodeVisible: false,
  sortDrawerVisible: false,
  isExpand: false,
  menuData: [] as Array<ManageResource>,
  selectedMenus: [] as Array<string>,
  updateIcon: false,
  keyWord: null as string,
  selectedMenu: null as ManageResource
});
const {
  iconDrawerVisible,
  importDialogVisible,
  moveNodeVisible,
  sortDrawerVisible,
  isExpand,
  menuData,
  selectedMenus,
  keyWord,
  selectedMenu
} = toRefs(state);

//获得菜单根节点数据
getMenuData({ parentId: "-1" }).then(result => {
  state.menuData = result.data;
});

//重新加载菜单数据
const reloadMenuData = async () => {
  state.menuData = [];
  await getMenuData({ parentId: "-1" }).then(result => {
    state.menuData = result.data;
  });
  menuCrud.value.refreshTable();
};

//快捷选择图标
const selectIcon = (row: any) => {
  menuForm.value = row as ManageResource;
  state.iconDrawerVisible = true;
  state.updateIcon = true;
};

//图标选择事件
const iconSelectHandler = async (iconName: string) => {
  menuForm.value.resourceIcon = iconName;
  //快捷更新图标
  if (state.updateIcon) {
    await updateMenu(menuForm.value).then(result => {
      if (result.status == ResultStatus.Success) {
        ElNotification({
          title: "提示",
          message: "已成功更新菜单图标！",
          type: "success"
        });
      }
    });
  }
  state.updateIcon = false;
};

//加载子节点
const treeLoadHandler = async (tree, treeNode, resolve) => {
  await getMenuData({ parentId: tree.resourceId }).then(result => {
    resolve(result.data);
  });
};

//新增菜单触发
const beforeFormOpenHandler = (done, type) => {
  if (type === "add") {
    //重置form表单内容
    menuForm.value = {
      resourceIcon: defaultMenuIcon,
      sortIndex: 0,
      openTarget: "Tab",
      isHidden: false,
      resourceType: "MENU",
      resourceTypeCode: "PC"
    } as ManageResource;
  }
  done();
};

//新增菜单
const rowSave = async (row, done) => {
  if (!row.parentId) {
    row.parentId = rootParentId;
  }
  await addMenu(row).then(result => {
    row.hidden = row.isHidden;
    if (result.status == ResultStatus.Success) {
      ElNotification({
        title: "提示",
        message: "已成功新增菜单信息！",
        type: "success"
      });
    }
    done(result.data);
  });
};

//新增子菜单
const addSubMenu = async row => {
  menuCrud.value.rowAdd();
  menuForm.value.parentId = row.resourceId;
};

//删除菜单
const rowDel = async (row, index, done) => {
  //删除操作确认
  const menu = row as ManageResource;
  const tipMessage = `确定要删除 ${menu.resourceName} ${
    menu.hasChildren ? "及其子菜单" : ""
  }么?`;
  await ElMessageBox.confirm(tipMessage, `提示`, {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning"
  });

  //执行菜单删除操作
  await deleteMenu(menu.resourceId).then(result => {
    if (result.status == ResultStatus.Success) {
      ElNotification({
        title: "提示",
        message: `已成功删除菜单：${menu.resourceName}`,
        type: "success"
      });
    }
  });
  done(row);
};

//更新菜单
const rowUpdate = async (row, index, done) => {
  row.hidden = row.isHidden;
  await updateMenu(row).then(result => {
    if (result.status == ResultStatus.Success) {
      ElNotification({
        title: "提示",
        message: "已成功保存菜单信息！",
        type: "success"
      });
    }
    done(row);
  });
};

//菜单选择变更触发
const selectionChangeHandler = (selRows: Array<ManageResource>) => {
  const selectedArray = [];
  selRows.forEach(row => selectedArray.push(row.resourceId));
  state.selectedMenus = selectedArray;
};

//导出菜单数据
const exportHandler = () => {
  $confirm(
    `您确认要导出已选择的 ${state.selectedMenus.length} 项菜单数据么？`,
    "提示",
    {
      confirmButtonText: "确定",
      cancelButtonText: "取消"
    }
  ).then(async () => {
    message("数据正在导出中...", { type: "success" });
    await exportMenuData({ resourceIdList: state.selectedMenus });
    menuCrud.value.clearSelection();
    state.selectedMenus = [];
  });
};

//菜单导入成功触发
const importSuccessHandler = () => {
  reloadMenuData();
};

// 是否展开table(展开与折叠切换)
const handleExpand = () => {
  state.isExpand = !state.isExpand;
  nextTick(() => {
    forArr(state.menuData, state.isExpand);
  });
};

// 递归菜单树
const forArr = (arr: Array<any>, isExpand: boolean) => {
  arr.forEach(i => {
    menuCrud.value.toggleRowExpansion(i, isExpand);
    if (i.children) {
      forArr(i.children, isExpand);
    }
  });
};

//移动菜单触发
const moveNodeHandler = (m: ManageResource) => {
  state.selectedMenu = m;
  state.moveNodeVisible = true;
};

//菜单排序触发
const sortMenuHandler = (m: ManageResource) => {
  state.selectedMenu = m;
  state.sortDrawerVisible = true;
};

//菜单排序成功触发
const sortSuccessHandler = (data: Array<ManageResource>) => {
  if (state.selectedMenu.resourceId === "-1") {
    reloadMenuData();
  } else {
    menuCrud.value.updateKeyChildren(state.selectedMenu.resourceId, data);
  }
};

//成功移动菜单触发
const moveMenuSuccessHandler = () => {
  reloadMenuData();
};
</script>
