<template>
  <el-tree-select
    ref="selRef"
    v-model="selectedDeptId"
    lazy
    :load="loadNode"
    :props="treeProps"
    node-key="deptId"
    check-strictly
    :render-after-expand="false"
    :data="data"
    :cache-data="cacheData"
    @change="deptChange"
    :style="customStyle"
  />
</template>

<script lang="ts" setup>
import {nextTick, onMounted, reactive, ref, toRefs, watch} from "vue";
import {useDeptStoreHook} from "@/store/modules/dept";
import {ElTreeSelect} from "element-plus";

const selRef = ref<InstanceType<typeof ElTreeSelect>>();

//组件属性
const props = defineProps({
  deptId: {
    type: String
  },
  deptName: {
    type: String
  },
  startRootId: {
    type: String
  },
  customStyle: {
    type: String,
    default: ''
  }
});

//定义事件
const emit = defineEmits(["changeDept", "update:deptId"]);

//树属性key映射
const treeProps = {
  label: "deptName",
  children: "children",
  isLeaf: "leaf"
};

//数据对象
const state = reactive({
  data: [],
  selectedDeptId: null as any,
  cacheData: [{deptId: props.deptId, deptName: props.deptName}]
});
const {data, cacheData, selectedDeptId} = toRefs(state);

watch(() => props.deptId, (newVal) => {
  if (newVal && newVal.length > 0) {
    state.cacheData[0].deptId = newVal;
    state.cacheData[0].deptName = useDeptStoreHook().getDept(newVal)?.deptName;
  }
  nextTick(() => {
    state.selectedDeptId = newVal;
  });
});

//初始化处理
onMounted(async () => {
  if (props.deptId) {
    state.selectedDeptId = props.deptId;
  }
  state.data = useDeptStoreHook().getChildren("-1");
});

//加载数据
const loadNode = (node, resolve) => {
  if (node.isLeaf) return resolve([]);
  resolve(useDeptStoreHook().getChildren(node.data.deptId));
};

//选择变更事件
const deptChange = (value: any) => {
  const currentNode = selRef.value!.getCurrentNode();
  emit("changeDept", currentNode);
  emit("update:deptId", value);
};
</script>
