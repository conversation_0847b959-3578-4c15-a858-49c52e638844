<template>
  <div class="p-3">
    <el-row :gutter="20">
      <el-col :xs="24" :sm="16">
        <el-card shadow="hover">
          <template #header>
            <div class="flex-bc">
              <div class="flex-sc space-x-1">
                <iconify-icon-offline
                  icon="RI-BookmarkFill"
                  :style="{ color: '#409EFF' }"
                />
                <span class="font-bold">基本信息</span>
              </div>
              <el-link
                type="primary"
                :icon="useRenderIcon('EP-Lock')"
                @click="updatePwdVisible = true"
              >
                变更密码
              </el-link>
            </div>
          </template>
          <div class="flex items-start space-x-10 h-[130px]">
            <div
              class="hover:cursor-pointer"
              @click="avatarSelectorVisible = true"
            >
              <iconify-icon-offline
                icon="RI-FileEditLine"
                class="float-right"
              />
              <el-avatar :src="avatarUrl" :size="120" class="shadow-lg">
                {{ currentUser.realName?.split("")[0] }}
              </el-avatar>
            </div>
            <div class="space-y-5">
              <span class="font-bold">
                {{ formatToGreetings(new Date()) }} {{ currentUser.realName }}
              </span>
              <el-descriptions :column="2" border>
                <el-descriptions-item :width="200">
                  <template #label>
                    <div class="flex-sc space-x-2 pl-3">
                      <iconify-icon-offline icon="EP-User" />
                      <span class="font-bold">用户账号</span>
                    </div>
                  </template>
                  {{ currentUser.userName }}
                </el-descriptions-item>
                <el-descriptions-item :width="200">
                  <template #label>
                    <div class="flex-sc space-x-2 pl-3">
                      <iconify-icon-offline icon="EP-CreditCard" />
                      <span class="font-bold">用户姓名</span>
                    </div>
                  </template>
                  {{ currentUser.realName }}
                </el-descriptions-item>
                <el-descriptions-item :width="200">
                  <template #label>
                    <div class="flex-sc space-x-2 pl-3">
                      <iconify-icon-offline icon="EP-Aim" />
                      <span class="font-bold">上次登录IP</span>
                    </div>
                  </template>
                  {{ currentUser.loginIp }}
                </el-descriptions-item>
                <el-descriptions-item :width="200">
                  <template #label>
                    <div class="flex-sc space-x-2 pl-3">
                      <iconify-icon-offline icon="EP-AlarmClock" />
                      <span class="font-bold">上次登录时间</span>
                    </div>
                  </template>
                  {{ currentUser.loginTime }}
                </el-descriptions-item>
              </el-descriptions>
            </div>
          </div>
        </el-card>
      </el-col>

      <el-col :xs="24" :sm="8">
        <el-card shadow="hover">
          <template #header>
            <div class="flex-bc">
              <div class="flex-sc space-x-1">
                <iconify-icon-offline
                  icon="RI-BookmarkFill"
                  :style="{ color: '#409EFF' }"
                />
                <span class="font-bold">消息通知</span>
              </div>
              <div class="flex items-center">
                <el-radio-group
                  v-model="messageQueryCondition.subject"
                  @change="loadMessageData"
                >
                  <el-radio-button label="0">待办</el-radio-button>
                  <el-radio-button label="1">通知</el-radio-button>
                </el-radio-group>
                <el-divider direction="vertical" border-style="dashed" />
                <el-link
                  type="primary"
                  :icon="useRenderIcon('EP-View')"
                  @click="toMessageCenter"
                >
                  查看全部
                </el-link>
              </div>
            </div>
          </template>
          <div class="h-[130px]">
            <el-table
              :data="messageData"
              :show-header="false"
              class="w-full"
              height="118"
              @row-click="rowClickHandler"
            >
              <el-table-column label="标题">
                <template #default="scope">
                  <div class="flex-sc">
                    <el-badge
                      v-if="!scope.row.status"
                      :value="1"
                      :is-dot="true"
                      class="pt-2 pr-1"
                    />
                    <el-link type="primary">
                      {{ scope.row.messageTitle }}
                    </el-link>
                  </div>
                </template>
              </el-table-column>
              <el-table-column label="时间" prop="createTime" width="150" />
              <el-table-column label="优先级" width="50">
                <template #default="scope">
                  <el-tag v-if="scope.row.priority === 0" type="danger">
                    高
                  </el-tag>
                  <el-tag v-if="scope.row.priority === 1" type="warning">
                    中
                  </el-tag>
                  <el-tag v-if="scope.row.priority === 2">低</el-tag>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </el-card>
      </el-col>

      <el-col :span="24" class="mt-5">
        <el-card shadow="hover">
          <template #header>
            <div class="flex-bc">
              <div class="flex-sc space-x-1">
                <iconify-icon-offline
                  icon="RI-BookmarkFill"
                  :style="{ color: '#409EFF' }"
                />
                <span class="font-bold">详细信息</span>
              </div>
              <el-link
                type="primary"
                :icon="useRenderIcon('EP-Edit')"
                @click="improveUserInfo"
              >
                完善信息
              </el-link>
            </div>
          </template>
          <div class="flex justify-center">
            <el-descriptions :column="2" border>
              <el-descriptions-item label="用户类型" :width="500">
                {{ currentUser.userTypeName }}
              </el-descriptions-item>
              <el-descriptions-item label="所属组织" :width="500">
                {{ currentUser.deptName }}
              </el-descriptions-item>
              <el-descriptions-item label="权限角色" :width="500">
                {{ currentUser.roleNames && currentUser.roleNames.join(",") }}
              </el-descriptions-item>
              <el-descriptions-item label="性别" :width="500">
                {{ currentUser.gender === "male" ? "男" : "女" }}
              </el-descriptions-item>
              <el-descriptions-item label="出生日期" :width="500">
                {{
                  currentUser.birthday != null
                    ? currentUser.birthday.substring(0, 10)
                    : ""
                }}
              </el-descriptions-item>
              <el-descriptions-item label="手机号" :width="500">
                {{ currentUser.cellPhone }}
              </el-descriptions-item>
              <el-descriptions-item label="固定电话" :width="500">
                {{ currentUser.telephone }}
              </el-descriptions-item>
              <el-descriptions-item label="电子邮件" :width="500">
                {{ currentUser.email }}
              </el-descriptions-item>
              <el-descriptions-item label="状态" :width="500">
                <el-tag v-if="currentUser.enableSwitch === '1'" type="success">
                  启用
                </el-tag>
                <el-tag v-else class="ml-2" type="danger">禁用</el-tag>
              </el-descriptions-item>
              <el-descriptions-item label="账号有效期" :width="500">
                {{ currentUser.expireTime }}
              </el-descriptions-item>
              <el-descriptions-item label="联系地址" :width="500">
                {{ currentUser.address }}
              </el-descriptions-item>
            </el-descriptions>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!--用户头像选择抽屉-->
    <avatar-selector
      v-model:visible="avatarSelectorVisible"
      @select="avatarSelectHandler"
    />

    <!-- 变更密码窗口 -->
    <update-user-pwd
      v-model:visible="updatePwdVisible"
      :cancel-btn-visible="true"
    />
  </div>
</template>

<script lang="ts" setup>
import { computed, getCurrentInstance, h, reactive, ref, toRefs } from "vue";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";
import { UserManageInfo } from "@/views/system/usermanage/api/UserManage";
import { formatToGreetings } from "@/utils/formattor";
import { ResultStatus } from "@/utils/http/types";
import { message } from "@/utils/message";
import AvatarSelector from "@/views/system/personal/components/AvatarSelector.vue";
import { useUserStoreHook } from "@/store/modules/user";
import { addDialog, closeAllDialog } from "@/components/ReDialog/index";
import MessageDetailInfo from "@/views/system/message/components/MessageDetailInfo.vue";
import { useUnReadMessageHook } from "@/store/modules/message";
import UserInfoImprove from "@/views/system/personal/components/UserInfoImprove.vue";
import {
  getRecentMessage,
  MessageTableInfo,
  updateReadStatus
} from "@/views/system/message/api/MessageManageApi";
import {
  getCurrentUserManageInfo,
  updateUserAvatar
} from "@/views/system/personal/api/PersonalApi";
import UpdateUserPwd from "@/views/system/personal/components/UpdateUserPwd.vue";

const { $router } = getCurrentInstance().appContext.config.globalProperties;

const messageDetailRef = ref<InstanceType<typeof MessageDetailInfo>>();
const userImproveRef = ref<InstanceType<typeof UserInfoImprove>>();

//数据对象
const state = reactive({
  avatarSelectorVisible: false,
  updatePwdVisible: false,
  currentUser: { userAvatar: null } as UserManageInfo,
  messageData: [] as Array<MessageTableInfo>,
  messageQueryCondition: {
    subject: 0,
    limit: 10
  }
});
const {
  currentUser,
  avatarSelectorVisible,
  updatePwdVisible,
  messageData,
  messageQueryCondition
} = toRefs(state);

//加载用户信息
const loadCurrentUserInfo = async () => {
  getCurrentUserManageInfo().then(res => (state.currentUser = res.data));
};
loadCurrentUserInfo();

//用户头像地址
const avatarUrl = computed(() =>
  state.currentUser.userAvatar != null
    ? "/avatar/" + state.currentUser.userAvatar
    : ""
);

//加载消息信息
const loadMessageData = async () => {
  state.messageData = [];
  getRecentMessage(
    state.messageQueryCondition.subject,
    state.messageQueryCondition.limit
  ).then(res => (state.messageData = res.data));
};
loadMessageData();

//头像选择触发
const avatarSelectHandler = async (avatarName: string) => {
  updateUserAvatar(avatarName).then(res => {
    if (res.status === ResultStatus.Success) {
      message("已成功更新用户头像！", { type: "success" });
      updateSessionAvatar(avatarName);
      loadCurrentUserInfo();
    }
  });
};

//更新Session会话中的头像信息
const updateSessionAvatar = (avatarName: string) => {
  useUserStoreHook().SET_AVATAR(avatarName);
};

//查看消息
const rowClickHandler = (msg: MessageTableInfo) => {
  addDialog({
    title: msg.messageTitle,
    fullscreenIcon: true,
    closeOnClickModal: true,
    hideFooter: true,
    contentRenderer: () => h(MessageDetailInfo, { ref: messageDetailRef }),
    open: () => {
      messageDetailRef.value.loadMessageInfo(msg.messageId);
    },
    closeCallBack: () => {
      updateReadStatus(msg.relationId).then(() => {
        if (!msg.status) {
          loadMessageData();
        }
        useUnReadMessageHook()?.loadUnReadCountData();
      });
    }
  });
};

//跳转到消息中心
const toMessageCenter = () => {
  $router.push({ name: "SystemConfig_Message_Center" });
};

//完善用户信息
const improveUserInfo = () => {
  addDialog({
    title: "完善用户信息",
    fullscreenIcon: true,
    closeOnClickModal: false,
    contentRenderer: () =>
      h(UserInfoImprove, {
        ref: userImproveRef,
        onSubmit: (res: boolean) => {
          if (res) {
            closeAllDialog();
            loadCurrentUserInfo();
          }
        }
      }),
    open: () => {
      userImproveRef.value.injectUserInfo(state.currentUser);
    },
    beforeSure: () => {
      userImproveRef.value.submitForm();
    }
  });
};
</script>
