import { http } from "@/utils/http";
import { ServerNames } from "@/utils/http/serverNames";

const basePath = `${ServerNames.portalServer}/ai/manage`;

//AI接入信息管理对象
export type AiManageInfo = {
  id: string;
  code: string;
  name: string;
  mark: string;
  avatar: string;
  accessKey: string;
  hideEnable: string;
  updateUid: string;
  updateTime: string;
  createTime: string;
  sortIndex: number;
};

//新增AI接人信息
const addAiInfo = (info: AiManageInfo) =>
  http.postJson<RestResult<string>>(`${basePath}/addAiInfo`, info);

//修改AI接人信息
const updateAiInfo = (info: AiManageInfo) =>
  http.postJson<RestResult<string>>(`${basePath}/updateAiInfo`, info);

//删除AI接人信息
const deleteAiInfo = (id: string) =>
  http.get<string, RestResult<string>>(`${basePath}/deleteAiInfo?id=${id}`);

//查询所有待管理的AI信息
const getAllInfo = () =>
  http.get<any, RestResult<Array<AiManageInfo>>>(`${basePath}/getAllInfo`);

export { addAiInfo, updateAiInfo, deleteAiInfo, getAllInfo };
