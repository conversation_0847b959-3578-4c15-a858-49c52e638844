<template>
  <div v-if="aiEnable" class="ml-1.5 mr-1">
    <avue-crud ref="crudRef" v-model="form" :data="aiInfoData" :option="tableOption" @row-save="addInfoHandler"
      @row-update="updateInfoHandler" @refresh-change="loadAccessData" @row-del="removeHandler">
      <template #menu-right>
        <div class="float-left flex-c mt-0.5">
          <el-input clearable v-model="searchKeyWord" :suffix-icon="useRenderIcon('EP-Search')" style="width: 260px"
            placeholder="名称或标识码" @blur="
    searchKeyWord = ($event.target as HTMLInputElement).value.trim()
    " />
        </div>
        <el-divider direction="vertical" border-style="dashed" />
      </template>
      <template #hideEnable="{ row }">
        <el-tag :type="row.hideEnable ? 'info' : 'success'">
          {{ row.hideEnable ? "隐藏" : "显示" }}
        </el-tag>
      </template>
    </avue-crud>
  </div>
  <div v-else class="flex-c w-full mt-3">
    <el-text type="warning">系统未开启AI功能</el-text>
  </div>
</template>
<script setup lang="ts">
import { getConfig } from "@/config";
import { computed, getCurrentInstance, reactive, toRefs } from "vue";
import { hasAuth } from "@/router/utils";
import { AdminEnum } from "@/utils/CommonTypes";
import { validSignId } from "@/utils/validator";
import { ResultStatus } from "@/utils/http/types";
import {
  addAiInfo,
  AiManageInfo,
  deleteAiInfo,
  getAllInfo,
  updateAiInfo
} from "@/views/system/aiaccess/api/AiManageApi";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";

//获取AI开关配置
const enableAI = getConfig()?.EnableAI;
const { $notify, $confirm } =
  getCurrentInstance().appContext.config.globalProperties;

//数据对象
const state = reactive({
  aiEnable: false,
  searchKeyWord: "",
  data: [] as Array<AiManageInfo>,
  form: {} as AiManageInfo
});
const { aiEnable, searchKeyWord, data, form } = toRefs(state);
state.aiEnable = enableAI;

//关键词搜索过滤计算
const aiInfoData = computed(() => {
  const searchKeyWord = state.searchKeyWord;

  // 处理空字符串情况
  if (searchKeyWord == null || searchKeyWord.length === 0) {
    return state.data;
  }

  return state.data.filter(item => {
    // 检查 item.name 和 item.code 是否为有效字符串
    const name = item.name || "";
    const code = item.code || "";

    return name.includes(searchKeyWord) || code.includes(searchKeyWord);
  });
});

//标识码校验
const validateCode = (rule, value, callback) => {
  if (validSignId(value)) {
    callback();
  } else {
    callback(new Error("格式错误,示例：sop_assistant"));
  }
};

//根据页面高度设置表格高度
const tableHeight = computed(() => {
  return document.documentElement.offsetHeight - 150;
});

const tableOption = reactive({
  align: "center",
  menuAlign: "center",
  border: true,
  stripe: true,
  rowKey: "id",
  height: tableHeight,
  menuWidth: 140,
  menu: hasAuth([AdminEnum.SuperAdmin, AdminEnum.SystemAdmin]),
  addBtn: hasAuth([AdminEnum.SuperAdmin, AdminEnum.SystemAdmin]),
  column: [
    {
      label: "排序",
      prop: "sortIndex",
      sortable: true,
      width: 70,
      type: "number",
      value: 0,
      min: 0,
      max: 999,
      order: -1
    },
    {
      label: "图标",
      prop: "avatar",
      type: "input",
      order: -2,
      maxlength: 200,
      showWordLimit: true,
      hide: true
    },
    {
      label: "名称",
      prop: "name",
      type: "input",
      maxlength: 30,
      showWordLimit: true,
      rules: [{ required: true, message: "请输入名称", trigger: "blur" }]
    },
    {
      label: "标识",
      prop: "code",
      type: "input",
      editDisabled: true,
      maxlength: 30,
      showWordLimit: true,
      rules: [
        { required: true, message: "请输入标识", trigger: "blur" },
        {
          validator: validateCode,
          trigger: "blur"
        }
      ]
    },
    {
      label: "接入密钥",
      prop: "accessKey",
      type: "input",
      maxlength: 200,
      showColumn: false,
      hide: true,
      showWordLimit: true,
      span: 24,
      rules: [{ required: true, message: "请输入接入密钥", trigger: "blur" }]
    },
    {
      label: "接入应用ID",
      prop: "appId",
      type: "input",
      maxlength: 60,
      showColumn: false,
      hide: true,
      showWordLimit: true,
      span: 24,
      rules: [{ required: true, message: "请输入接入应用ID", trigger: "blur" }]
    },
    {
      label: "描述",
      prop: "mark",
      type: "input",
      maxlength: 120,
      showWordLimit: true,
      span: 24
    },
    {
      label: "隐藏",
      prop: "hideEnable",
      type: "switch",
      dicData: [
        { label: "隐藏", value: true },
        { label: "显示", value: false }
      ],
      value: false
    },
    {
      label: "更新人",
      prop: "updateUid",
      editDisplay: false,
      addDisplay: false
    },
    {
      label: "更新时间",
      prop: "updateTime",
      sortable: true,
      editDisplay: false,
      addDisplay: false
    },
    {
      label: "创建时间",
      prop: "createTime",
      sortable: true,
      editDisplay: false,
      addDisplay: false
    }
  ]
});

//新增接入信息
const addInfoHandler = async (row, done, loading) => {
  const res = await addAiInfo(row);
  if (res.status == ResultStatus.Success) {
    done();
    $notify({
      title: "提示",
      message: "已成功添加AI接入信息！",
      type: "success"
    });
    await loadAccessData();
  } else {
    loading(false);
  }
};

//更新接入信息
const updateInfoHandler = async (row, index, done, loading) => {
  const res = await updateAiInfo(row);
  if (res.status == ResultStatus.Success) {
    done();
    $notify({
      title: "提示",
      message: "已成功更新AI接入信息！",
      type: "success"
    });
    await loadAccessData();
  } else {
    loading(false);
  }
};

//删除接入信息
const removeHandler = async (row, index, done) => {
  await $confirm(`您确定要删除 '${row.name}' AI接入信息么？`, "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning"
  });
  const res = await deleteAiInfo(row.id);
  if (res.status == ResultStatus.Success) {
    $notify({
      title: "提示",
      message: "已成功删除AI接入信息！",
      type: "success"
    });
    done(row);
  }
};

//加载接入数据
const loadAccessData = async () => {
  state.data = [];
  const res = await getAllInfo();
  state.data = res.data;
};
loadAccessData();
</script>
