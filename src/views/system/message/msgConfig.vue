<template>
  <div>
    <splitpane :splitSet="settingLR">
      <template #paneL>
        <div class="pb-1.5 ml-1">
          <el-input
            clearable
            v-model="moduleKeyWord"
            :suffix-icon="useRenderIcon('EP-Search')"
          >
            <template #prepend>
              <el-button
                type="primary"
                :icon="useRenderIcon('RI-AddLine')"
                @click="addModule"
                v-auth="[AdminEnum.SuperAdmin, AdminEnum.SystemAdmin]"
              >
                添加
              </el-button>
            </template>
          </el-input>
        </div>
        <div
          class="border border-gray-400 border-opacity-20 rounded-lg h-content overflow-y-auto ml-1 dark:bg-dark-color"
        >
          <el-tree
            ref="moduleTreeRef"
            :data="moduleData"
            node-key="moduleId"
            :expand-on-click-node="false"
            highlight-current
            :props="moduleTreeProps"
            @current-change="moduleSelectChange"
            :filter-node-method="filterModule"
          >
            <template #default="{ node, data }">
              <div class="tree-list-content">
                <div class="tree-node-left">
                  <IconifyIconOffline icon="EP-Document" />
                  <span>{{ node.label }}</span>
                </div>

                <div
                  class="space-x-2"
                  v-auth="[AdminEnum.SuperAdmin, AdminEnum.SystemAdmin]"
                >
                  <el-link
                    type="primary"
                    :icon="useRenderIcon('RI-EditBoxFill')"
                    @click.stop="openModuleEditDialog(data)"
                  />
                  <el-link
                    type="danger"
                    :icon="useRenderIcon('RI-DeleteBin6Fill')"
                    @click.stop="deleteMessageModuleHandler(data)"
                  />
                </div>
              </div>
            </template>
          </el-tree>
        </div>
      </template>
      <template #paneR>
        <div class="ml-1.5 mr-1">
          <avue-crud
            :data="typeFilterData"
            :option="typeTableOption"
            @refresh-change="loadTypeData"
            @row-save="adMessageTypeHandler"
            @row-update="updateMessageTypeHandler"
            @row-del="deleteMessageTypeHandler"
          >
            <template #menu-right="{ size }">
              <div class="float-left pr-3">
                <el-input
                  clearable
                  :suffix-icon="useRenderIcon('EP-Search')"
                  :size="size"
                  placeholder="类型标识/类型名称"
                  v-model="typeKeyWord"
                  @blur="
                    typeKeyWord = (
                      $event.target as HTMLInputElement
                    ).value.trim()
                  "
                />
              </div>
            </template>
          </avue-crud>
        </div>
      </template>
    </splitpane>
  </div>
</template>

<script lang="ts" setup>
import splitpane, { ContextProps } from "@/components/ReSplitPane";
import { ResultStatus } from "@/utils/http/types";
import { validSignId } from "@/utils/validator";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";
import { ElTree } from "element-plus";
import { addDialog, closeAllDialog } from "@/components/ReDialog/index";
import MessageModuleEditor from "@/views/system/message/components/MessageModuleEditor.vue";
import { AdminEnum } from "@/utils/CommonTypes";
import { hasAuth } from "@/router/utils";
import {
  computed,
  getCurrentInstance,
  h,
  nextTick,
  reactive,
  ref,
  toRefs,
  watch
} from "vue";
import {
  addMessageType,
  deleteMessageType,
  deleteModule,
  getAllModuleData,
  getMessageTypeData,
  MessageModuleInfo,
  MessageTypeInfo,
  updateMessageType
} from "@/views/system/message/api/MessageManageApi";

defineOptions({
  name: "SystemConfig_Message_MetaDataManage"
});

const { $notify, $confirm } =
  getCurrentInstance().appContext.config.globalProperties;
const moduleTreeRef = ref<InstanceType<typeof ElTree>>();
const editModuleRef = ref<InstanceType<typeof MessageModuleEditor>>();

const settingLR: ContextProps = reactive({
  minPercent: 10,
  defaultPercent: 15,
  split: "vertical"
});

const moduleTreeProps = {
  label: "moduleName"
};

//类型标识校验
const validateTypeId = (rule, value, callback) => {
  if (validSignId(value)) {
    callback();
  } else {
    callback(new Error("格式错误,示例：dept_update"));
  }
};

const tableHeight = computed(() => {
  return document.documentElement.offsetHeight - 147;
});
const typeTableOption = reactive({
  align: "center",
  menuAlign: "center",
  stripe: true,
  border: true,
  labelWidth: 130,
  searchLabelWidth: 120,
  menuWidth: 180,
  refreshBtn: true,
  columnBtn: true,
  searchBtn: false,
  searchShow: false,
  height: tableHeight,
  rowKey: "typeId",
  menu: hasAuth([AdminEnum.SuperAdmin, AdminEnum.SystemAdmin]),
  addBtn: hasAuth([AdminEnum.SuperAdmin, AdminEnum.SystemAdmin]),
  column: [
    {
      label: "序号",
      prop: "sortIndex",
      width: 60,
      type: "number",
      value: 0,
      min: 0,
      max: 999,
      order: 1
    },
    {
      label: "类型标识",
      prop: "typeId",
      required: true,
      maxlength: 32,
      showWordLimit: true,
      editDisabled: true,
      rules: [
        { required: true, message: "请输类型标识", trigger: "blur" },
        { validator: validateTypeId, trigger: "blur" }
      ],
      order: 3
    },
    {
      label: "类型名称",
      prop: "typeName",
      maxlength: 100,
      showWordLimit: true,
      rules: [{ required: true, message: "请输类型名称", trigger: "blur" }],
      order: 2
    },
    {
      label: "创建时间",
      prop: "createTime",
      addDisplay: false,
      editDisplay: false
    },
    {
      label: "更新时间",
      prop: "updateTime",
      addDisplay: false,
      editDisplay: false
    },
    {
      label: "更新用户ID",
      prop: "updateUid",
      addDisplay: false,
      editDisplay: false
    }
  ],
  defaultSort: {
    prop: "sortIndex",
    order: "ascending"
  }
});

//数据对象
const state = reactive({
  moduleData: [] as Array<MessageModuleInfo>,
  typeData: [] as Array<MessageTypeInfo>,
  moduleKeyWord: "",
  typeKeyWord: ""
});

const { moduleData, moduleKeyWord, typeKeyWord } = toRefs(state);

watch(moduleKeyWord, value => moduleTreeRef.value!.filter(value));

//加载消息模块数据
const loadModuleData = async () => {
  getAllModuleData().then(res => {
    state.moduleData = res.data;

    //默认选中第一个
    if (state.moduleData && state.moduleData.length > 0) {
      const firstModule = state.moduleData[0];
      nextTick(() => {
        moduleTreeRef.value!.setCurrentKey(firstModule.moduleId);
      });
    } else {
      moduleTreeRef.value!.setCurrentKey(null);
    }
  });
};
loadModuleData();

//模块选择
const moduleSelectChange = async (module: MessageModuleInfo) => {
  state.typeData = [];
  getMessageTypeData(module.moduleId).then(res => {
    state.typeData = res.data;
  });
};

//加载消息类型数据
const loadTypeData = async () => {
  state.typeData = [];
  const moduleId = moduleTreeRef.value.getCurrentKey();
  getMessageTypeData(moduleId).then(res => {
    state.typeData = res.data;
  });
};

//新增模块
const addModule = () => {
  openModuleEditDialog({
    moduleId: "",
    moduleName: "",
    createTime: "",
    updateTime: "",
    updateUid: "",
    sortIndex: 0
  });
};

//打开模块编辑窗口
const openModuleEditDialog = (module: MessageModuleInfo) => {
  addDialog({
    title: module.moduleId ? "编辑模块" : "新增模块",
    fullscreenIcon: true,
    closeOnClickModal: false,
    props: module,
    contentRenderer: () =>
      h(MessageModuleEditor, {
        ref: editModuleRef,
        onSubmit: (res: boolean) => {
          if (res) {
            closeAllDialog();
            loadModuleData();
          }
        }
      }),
    beforeSure: () => {
      editModuleRef.value.submitData();
    }
  });
};

//删除模块
const deleteMessageModuleHandler = async (module: MessageModuleInfo) => {
  $confirm(`确定要删除 '${module.moduleName}' 么？`, "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning"
  }).then(() => {
    deleteModule(module.moduleId).then(res => {
      if (res.status === ResultStatus.Success) {
        $notify({
          title: "提示",
          message: "已成功删除消息模块！",
          type: "success"
        });
        loadModuleData();
      }
    });
  });
};

//新增类型
const adMessageTypeHandler = async (row, done) => {
  row.moduleId = moduleTreeRef.value.getCurrentKey();
  addMessageType(row).then(res => {
    if (res.status === ResultStatus.Success) {
      $notify({
        title: "提示",
        message: "已成功添加类型信息！",
        type: "success"
      });
      loadTypeData();
    }
    done();
  });
};

//更新类型
const updateMessageTypeHandler = async (row, index, done) => {
  updateMessageType(row).then(res => {
    if (res.status == ResultStatus.Success) {
      $notify({
        title: "提示",
        message: "已成功更新类型信息！",
        type: "success"
      });
      loadTypeData();
      done();
    }
  });
};

//删除类型
const deleteMessageTypeHandler = async (row, index, done) => {
  $confirm(`确定要删除 '${row.typeName}' 么？`, "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning"
  }).then(() => {
    deleteMessageType(row.typeId).then(res => {
      if (res.status == ResultStatus.Success) {
        $notify({
          title: "提示",
          message: "已成功删除类型信息！",
          type: "success"
        });
        done(row);
      }
    });
  });
};

//消息模块搜索
const filterModule = (value: string, data: MessageModuleInfo) => {
  if (!value) return true;
  return data.moduleName.includes(value);
};

//消息类型数据过滤
const typeFilterData = computed(() => {
  if (state.typeKeyWord != null && state.typeKeyWord.length > 0) {
    return state.typeData.filter(item => {
      if (
        item.typeName.includes(state.typeKeyWord) ||
        item.typeId.includes(state.typeKeyWord)
      ) {
        return item;
      }
    });
  } else {
    return state.typeData;
  }
});
</script>

<style scoped>
.h-content {
  height: calc(100% - 48px);
}
</style>
