/* 全局响应式样式 */

/* 大屏幕 (>=1920px) */
@media screen and (min-width: 1920px) {
  .comprehensive-situation {
    font-size: 16px;
  }
}

/* 中等屏幕 (>=1366px 且 <1920px) */
@media screen and (min-width: 1366px) and (max-width: 1919px) {
  .comprehensive-situation {
    font-size: 14px;
  }
  
  .topology-title-img {
    transform: scale(0.9);
    transform-origin: left center;
  }
}

/* 小屏幕 (>=1024px 且 <1366px) */
@media screen and (min-width: 1024px) and (max-width: 1365px) {
  .comprehensive-situation {
    font-size: 12px;
  }
  
  .topology-title-img {
    transform: scale(0.85);
    transform-origin: left center;
  }
  
  .operation-bar {
    font-size: 12px;
    gap: 5px;
  }
}

/* 平板 (>=768px 且 <1024px) */
@media screen and (min-width: 768px) and (max-width: 1023px) {
  .comprehensive-situation {
    font-size: 12px;
  }
  
  .topology-title-img {
    transform: scale(0.8);
    transform-origin: left center;
  }
  
  .operation-bar {
    font-size: 11px;
    gap: 4px;
  }
  
  .topology-container {
    height: 32vh !important;
  }
  
  .topology-chart {
    height: 28vh !important;
  }
}

/* 手机 (<768px) */
@media screen and (max-width: 767px) {
  .comprehensive-situation {
    font-size: 12px;
  }
  
  .main-content {
    flex-direction: column !important;
  }
  
  .left-panel,
  .right-panel,
  .center-panel {
    width: 100% !important;
    margin-bottom: 10px;
  }
  
  .system-run-container {
    flex-direction: column !important;
  }
  
  .alarm-overview-container,
  .alarm-type-container,
  .alarm-statistics-container {
    width: 100% !important;
    margin-bottom: 10px;
  }
  
  .topology-title-img {
    transform: scale(0.7);
    transform-origin: left center;
  }
  
  .operation-bar {
    font-size: 10px;
    gap: 3px;
  }
  
  .topology-container {
    height: 30vh !important;
  }
  
  .topology-chart {
    height: 25vh !important;
  }
}

/* 确保图表和图形在所有屏幕尺寸下都能正确显示 */
.el-chart {
  width: 100% !important;
  height: 100% !important;
}

/* 确保图片不会溢出容器 */
img {
  max-width: 100%;
  height: auto;
}

/* 确保文本不会溢出容器 */
.text-ellipsis {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 确保表格在小屏幕上可以水平滚动 */
.el-table {
  width: 100% !important;
  overflow-x: auto;
}

/* 确保弹出框不会超出屏幕 */
.el-dialog,
.el-message-box,
.el-popover {
  max-width: 95vw;
  max-height: 95vh;
  overflow: auto;
}

/* 确保下拉菜单在小屏幕上不会被截断 */
.el-dropdown-menu {
  max-height: 80vh;
  overflow-y: auto;
}
