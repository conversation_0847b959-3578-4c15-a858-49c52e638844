<script setup lang="ts">
import { defineComponent, computed } from 'vue';
import userUsageTitleIcon from '@/views/comprehensiveSituationPresentation/assets/用户数-图标@3x.png';

defineComponent({
  name: 'UserUsageSituation'
});

// 定义数据项类型
interface DataItem {
  id: string;
  value: number;
  label: string;
  unit: string;
  icon?: string;
}

// 定义数据行类型
interface DataRow {
  id: string;
  icon?: string;
  items: DataItem[];
}

// 定义热门应用数据类型
interface HotAppItem {
  id: string;
  name: string;
  count: number;
  rank: number;
}

// 定义组件接收的属性
interface Props {
  rows?: DataRow[];
  hotApps?: HotAppItem[]; // 新增热门应用数据
}

// 设置默认值
const props = withDefaults(defineProps<Props>(), {
  rows: () => [],
  hotApps: () => []
});

// 获取图标路径
const getIconPath = (iconName: string) => {
  try {
    return new URL(`/src/views/comprehensiveSituationPresentation/assets/${iconName}`, import.meta.url).href;
  } catch (error) {
    console.error(`加载图标失败: ${iconName}`, error);
    return '';
  }
};

// 获取排名标签的样式类
const getRankClass = (rank: number) => {
  switch (rank) {
    case 1: return 'rank-top1';
    case 2: return 'rank-top2';
    case 3: return 'rank-top3';
    default: return 'rank-other';
  }
};

// 计算进度条的百分比
const getProgressPercent = (count: number, maxCount: number) => {
  return Math.round((count / maxCount) * 100);
};

// 获取最大数值用于计算进度条
const maxCount = computed(() => {
  if (props.hotApps.length === 0) return 1;
  return Math.max(...props.hotApps.map(app => app.count));
});
</script>

<template>
  <div class="user-usage-situation">
    <!-- 标题区域 -->
    <div class="title-section">
      <img :src="userUsageTitleIcon" alt="用户使用态势" class="title-icon" />
      <span class="title-text">用户使用态势</span>
    </div>

    <!-- 用户数据统计区域 -->
    <div class="stats-section">
      <!-- 动态生成数据行 -->
      <div v-for="row in props.rows" :key="row.id" class="stats-row">
        <!-- 第一个数据项，带图标 -->
        <div v-if="row.items.length > 0" class="stats-item">
          <div v-if="row.icon" class="stats-icon">
            <img :src="getIconPath(row.icon)" :alt="row.items[0].label" />
          </div>
          <div class="stats-content">
            <div class="stats-value">{{ row.items[0].value }} <span class="stats-unit">({{ row.items[0].unit }})</span></div>
            <div class="stats-label">{{ row.items[0].label }}</div>
          </div>
        </div>

        <!-- 其余数据项 -->
        <div v-for="(item, index) in row.items.slice(1)" :key="item.id" class="stats-item">
          <div class="stats-content">
            <div class="stats-value">{{ item.value }} <span class="stats-unit">({{ item.unit }})</span></div>
            <div class="stats-label">{{ item.label }}</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 热门应用TOP5区域 -->
    <div class="hot-apps-section">
      <!-- 热门应用标题 -->
      <div class="hot-apps-title">
        <span class="hot-apps-icon">📊</span>
        <span class="hot-apps-text">热门应用TOP5</span>
      </div>

      <!-- 热门应用排行榜 -->
      <div class="hot-apps-list">
        <div v-for="app in props.hotApps" :key="app.id" class="hot-app-item">
          <!-- 左侧排名标签和应用名称区域 -->
          <div class="left-section">
            <!-- 排名标签 -->
            <div class="rank-badge" :class="getRankClass(app.rank)">
              Top{{ app.rank }}
            </div>

            <!-- 应用名称 -->
            <div class="app-name">{{ app.name }}</div>
          </div>

          <!-- 右侧进度条和数值区域 -->
          <div class="right-section">
            <!-- 进度条 -->
            <div class="progress-container">
              <div class="progress-bar" :class="getRankClass(app.rank)"
                   :style="{ width: getProgressPercent(app.count, maxCount) + '%' }">
              </div>
            </div>

            <!-- 数值（带虚线框） -->
            <div class="app-count">{{ app.count }}</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.user-usage-situation {
  width: 100%;
  height: 100%;
  padding: 16px;
  background: #ffffff;

  // 标题区域
  .title-section {
    display: flex;
    align-items: center;
    margin-bottom: 20px;

    .title-icon {
      width: 24px;
      height: 24px;
      margin-right: 8px;
    }

    .title-text {
      font-size: 16px;
      font-weight: 600;
      color: #1976d2;
    }
  }

  // 统计数据区域
  .stats-section {
    margin-bottom: 24px;

    .stats-row {
      display: flex;
      align-items: center;
      margin-bottom: 16px;
      gap: 40px;

      &:last-child {
        margin-bottom: 0;
      }
    }

    .stats-item {
      display: flex;
      align-items: center;

      .stats-icon {
        margin-right: 12px;

        img {
          width: 32px;
          height: 32px;
          object-fit: contain;
        }
      }

      .stats-content {
        display: flex;
        flex-direction: column;

        .stats-value {
          font-size: 18px;
          font-weight: bold;
          color: #333333;
          line-height: 1.2;

          .stats-unit {
            font-size: 14px;
            color: #666666;
            font-weight: normal;
            margin-left: 2px;
          }
        }

        .stats-label {
          font-size: 12px;
          color: #666666;
          margin-top: 2px;
          white-space: nowrap;
        }
      }
    }
  }

  // 热门应用区域
  .hot-apps-section {
    .hot-apps-title {
      display: flex;
      align-items: center;
      margin-bottom: 16px;

      .hot-apps-icon {
        font-size: 16px;
        margin-right: 6px;
      }

      .hot-apps-text {
        font-size: 14px;
        font-weight: 600;
        color: #1976d2;
      }
    }

    .hot-apps-list {
      .hot-app-item {
        display: flex;
        align-items: center;
        margin-bottom: 8px;

        &:last-child {
          margin-bottom: 0;
        }

        .left-section {
          display: flex;
          align-items: center;
          gap: 8px;
          width: 120px;
          flex-shrink: 0;

          .rank-badge {
            width: 48px;
            height: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            font-weight: bold;
            color: #ffffff;
            flex-shrink: 0;

            &.rank-top1 {
              background: #ff3838;
            }

            &.rank-top2 {
              background: #ff7716;
            }

            &.rank-top3 {
              background: #e4c513;
            }

            &.rank-other {
              background: #0095ff;
            }
          }

          .app-name {
            font-size: 12px;
            color: #333333;
            flex: 1;
          }
        }

        .right-section {
          flex: 1;
          display: flex;
          align-items: center;
          gap: 8px;

          .progress-container {
            flex: 1;
            height: 12px;
            background: #f0f0f0;
            overflow: hidden;

            .progress-bar {
              height: 100%;
              transition: width 0.3s ease;

              &.rank-top1 {
                background: #ff3838;
              }

              &.rank-top2 {
                background: #ff7716;
              }

              &.rank-top3 {
                background: #e4c513;
              }

              &.rank-other {
                background: #0095ff;
              }
            }
          }

          .app-count {
            font-size: 14px;
            font-weight: bold;
            color: #1976d2;
            width: 50px;
            text-align: center;
            flex-shrink: 0;
            border: 2px dashed #87ceeb;
            padding: 2px 4px;
            background: rgba(135, 206, 235, 0.1);
          }
        }
      }
    }
  }
}
</style>
