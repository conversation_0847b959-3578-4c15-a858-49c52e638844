<script setup lang="ts">
import { defineComponent } from 'vue';

defineComponent({
  name: 'UserUsageSituation'
});

// 定义数据项类型
interface DataItem {
  id: string;
  value: number;
  label: string;
  unit: string;
  icon?: string;
}

// 定义数据行类型
interface DataRow {
  id: string;
  icon?: string;
  items: DataItem[];
}

// 定义组件接收的属性
interface Props {
  rows?: DataRow[];
}

// 设置默认值
const props = withDefaults(defineProps<Props>(), {
  rows: () => []
});

// 获取图标路径
const getIconPath = (iconName: string) => {
  try {
    return new URL(`/src/views/comprehensiveSituationPresentation/assets/${iconName}`, import.meta.url).href;
  } catch (error) {
    console.error(`加载图标失败: ${iconName}`, error);
    return '';
  }
};
</script>

<template>
  <div class="user-usage-situation">
    <div class="data-grid">
      <!-- 动态生成数据行 -->
      <div v-for="row in props.rows" :key="row.id" class="data-row">
        <!-- 第一个数据项，带图标 -->
        <div v-if="row.items.length > 0" class="data-item">
          <div v-if="row.icon" class="icon-container">
            <img :src="getIconPath(row.icon)" :alt="row.items[0].label" class="data-icon" />
          </div>
          <div class="data-content">
            <div class="data-value">{{ row.items[0].value }} <span class="unit">({{ row.items[0].unit }})</span></div>
            <div class="data-label">{{ row.items[0].label }}</div>
          </div>
        </div>

        <!-- 其余数据项 -->
        <div v-for="(item, index) in row.items.slice(1)" :key="item.id" 
             class="data-item" :class="{ 'data-item-right': index > 0 }">
          <div v-if="item.icon" class="icon-container">
            <img :src="getIconPath(item.icon)" :alt="item.label" class="data-icon" />
          </div>
          <div class="data-content">
            <div class="data-value">{{ item.value }} <span class="unit">({{ item.unit }})</span></div>
            <div class="data-label">{{ item.label }}</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.user-usage-situation {
  width: 100%;
  height: 100%;
  padding: 10px;
  
  .data-grid {
    display: flex;
    flex-direction: column;
    gap: 10px;
    height: 100%;
    justify-content: space-around;
  }
  
  .data-row {
    display: flex;
    align-items: center;
    gap: 3rem;
    justify-content: flex-start;
    
    &:first-child {
      margin-bottom: 5px;
    }
  }
  
  .data-item {
    display: flex;
    align-items: center;
    
    &.data-item-right {
      margin-left: 20px;
    }
    
    .icon-container {
      margin-right: 15px;
      
      .data-icon {
        width: 36px;
        height: 36px;
        object-fit: contain;
        filter: drop-shadow(0 0 3px rgba(64, 158, 255, 0.3));
      }
    }
    
    .data-content {
      display: flex;
      flex-direction: column;
      
      .data-value {
        font-size: 16px;
        font-weight: bold;
        color: #1976d2;
        line-height: 1.2;
        text-shadow: 0 0 5px rgba(25, 118, 210, 0.3);
        font-family: 'Arial', sans-serif;
        white-space: nowrap;
        .unit {
          font-size: 14px;
          color: #7b8794;
          opacity: 0.8;
          margin-left: 2px;
          font-weight: normal;
        }
      }

      .data-label {
        font-size: 13px;
        white-space: nowrap;
        color: #2c3e50;
        opacity: 0.8;
        margin-top: 2px;
      }
    }
  }
}
</style>
