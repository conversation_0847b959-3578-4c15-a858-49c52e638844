import { ref } from 'vue';
import axios from 'axios';

// 导入图标资源
import securityIcon from '@/views/comprehensiveSituationPresentation/assets/颜值密码-图标-蓝@3x.png';
import portalIcon from '@/views/comprehensiveSituationPresentation/assets/门户应用-图标-蓝@3x.png';
import todoIcon from '@/views/comprehensiveSituationPresentation/assets/待办／已办-图标-蓝@3x.png';
import processIcon from '@/views/comprehensiveSituationPresentation/assets/流程起草-图标-蓝@3x.png';
import tomcatIcon from '@/views/comprehensiveSituationPresentation/assets/tomcat-图标-蓝@3x.png';
import mysqlIcon from '@/views/comprehensiveSituationPresentation/assets/Mysql-图标-蓝@3x.png';
import redisIcon from '@/views/comprehensiveSituationPresentation/assets/Redis-图标-蓝@3x.png';
import serverIcon from '@/views/comprehensiveSituationPresentation/assets/服务器-图标@3x.png';
import cloudIcon from '@/views/comprehensiveSituationPresentation/assets/云平台-图标-蓝@3x.png';

// 定义节点类型
export interface NodeType {
  id: string;
  name: string;
  type: string; // 'circle' | 'server' | 自定义类型
  icon: string;
  layer: string;
  position?: { x: number; y: number };
  status?: string; // 'normal' | 'warning' | 'error' | 'offline'
  data?: Record<string, any>; // 额外数据
}

// 定义连线类型
export interface EdgeType {
  id: string;
  source: string; // 源节点ID
  target: string; // 目标节点ID
  type?: string; // 连线类型
  status?: string; // 'normal' | 'warning' | 'error'
  data?: Record<string, any>; // 额外数据
}

// 定义图层类型
export interface LayerType {
  id: string;
  name: string;
  order: number; // 层级顺序，从上到下
}

// 定义拓扑数据类型
export interface TopologyDataType {
  nodes: NodeType[];
  edges: EdgeType[];
  layers: LayerType[];
}

/**
 * 拓扑数据加载Hook
 */
export function useTopologyData() {
  const isLoading = ref(false);
  const error = ref<string | null>(null);

  /**
   * 加载拓扑数据
   * @param url API地址
   * @returns 拓扑数据
   */
  const loadTopologyData = async (url: string): Promise<TopologyDataType> => {
    isLoading.value = true;
    error.value = null;

    try {
      const response = await axios.get(url);

      // 验证数据格式
      if (!response.data || typeof response.data !== 'object') {
        throw new Error('返回数据格式错误');
      }

      // 处理返回的数据
      const result: TopologyDataType = {
        nodes: Array.isArray(response.data.nodes) ? response.data.nodes : [],
        edges: Array.isArray(response.data.edges) ? response.data.edges : [],
        layers: Array.isArray(response.data.layers) ? response.data.layers : []
      };

      return result;
    } catch (err) {
      error.value = err instanceof Error ? err.message : '加载拓扑数据失败';
      throw err;
    } finally {
      isLoading.value = false;
    }
  };

  /**
   * 模拟加载拓扑数据（用于开发测试）
   * @returns 拓扑数据
   */
  const mockTopologyData = (): TopologyDataType => {
    // 默认图层
    const layers: LayerType[] = [
      { id: 'application', name: '应用服务层', order: 1 },
      { id: 'middleware', name: '中间件层', order: 2 },
      { id: 'infrastructure', name: '基础设施层', order: 3 }
    ];

    // 默认节点
    const nodes: NodeType[] = [
      // 应用服务层节点
      { id: 'security', name: '安全验证', type: 'circle', icon: securityIcon, layer: 'application' },
      { id: 'portal', name: '门户应用', type: 'circle', icon: portalIcon, layer: 'application' },
      { id: 'todo', name: '待办/已办数据加载', type: 'circle', icon: todoIcon, layer: 'application' },
      { id: 'process', name: '流程起草', type: 'circle', icon: processIcon, layer: 'application' },

      // 中间件层节点
      { id: 'tomcat1', name: 'Tomcat1', type: 'circle', icon: tomcatIcon, layer: 'middleware' },
      { id: 'tomcat2', name: 'Tomcat2', type: 'circle', icon: tomcatIcon, layer: 'middleware' },
      { id: 'mysql', name: 'Mysql', type: 'circle', icon: mysqlIcon, layer: 'middleware' },
      { id: 'redis', name: 'Redis', type: 'circle', icon: redisIcon, layer: 'middleware' },

      // 基础设施层节点
      { id: 'server1', name: '服务器1', type: 'server', icon: serverIcon, layer: 'infrastructure' },
      { id: 'server2', name: '服务器2', type: 'server', icon: serverIcon, layer: 'infrastructure' },
      { id: 'cloud', name: '云平台', type: 'server', icon: cloudIcon, layer: 'infrastructure' },
      { id: 'server3', name: '服务器3', type: 'server', icon: serverIcon, layer: 'infrastructure' }
    ];

    // 默认连线
    const edges: EdgeType[] = [
      // 应用层到中间件层
      { id: 'e1', source: 'security', target: 'tomcat1' },
      { id: 'e2', source: 'security', target: 'tomcat2' },
      { id: 'e3', source: 'portal', target: 'tomcat1' },
      { id: 'e4', source: 'portal', target: 'tomcat2' },
      { id: 'e5', source: 'todo', target: 'mysql' },
      { id: 'e6', source: 'process', target: 'redis' },

      // 中间件层到基础设施层
      { id: 'e7', source: 'tomcat1', target: 'server1' },
      { id: 'e8', source: 'tomcat1', target: 'server2' },
      { id: 'e9', source: 'tomcat2', target: 'server2' },
      { id: 'e10', source: 'mysql', target: 'cloud' },
      { id: 'e11', source: 'redis', target: 'server3' }
    ];

    return { nodes, edges, layers };
  };

  return {
    isLoading,
    error,
    loadTopologyData,
    mockTopologyData
  };
}
