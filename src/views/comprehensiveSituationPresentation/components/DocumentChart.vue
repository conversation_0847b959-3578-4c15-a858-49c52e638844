<template>
  <div ref="chartRef" class="document-chart"></div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch, onUnmounted, defineExpose } from 'vue';
import * as echarts from 'echarts/core';
import { BarChart } from 'echarts/charts';
import {
  GridComponent,
  TooltipComponent,
  LegendComponent
} from 'echarts/components';
import { CanvasRenderer } from 'echarts/renderers';

// 注册必要的组件
echarts.use([
  BarChart,
  GridComponent,
  TooltipComponent,
  LegendComponent,
  CanvasRenderer
]);

// 定义图表数据类型
interface ChartDataItem {
  date: string;
  documentProcessing: number;
  documentSeal: number;
}

const props = defineProps<{
  chartData: ChartDataItem[];
}>();

const chartRef = ref<HTMLElement | null>(null);
let chartInstance: echarts.ECharts | null = null;

// 初始化图表
const initChart = () => {
  if (!chartRef.value) return;

  // 创建图表实例
  chartInstance = echarts.init(chartRef.value);

  // 设置响应式
  window.addEventListener('resize', handleResize);

  // 渲染图表
  updateChart();
};

// 更新图表数据
const updateChart = () => {
  if (!chartInstance) return;

  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      },
      formatter: (params: any) => {
        const date = params[0].axisValue;
        let html = `<div style="margin: 0px 0 0; line-height: 1;">${date}</div>`;
        params.forEach((item: any) => {
          html += `<div style="margin: 10px 0 0; line-height: 1;">
            <div style="margin: 0px 0 0; line-height: 1;">
              <div style="margin: 0px 0 0; line-height: 1;">
                <span style="display:inline-block;margin-right:4px;border-radius:10px;width:10px;height:10px;background-color:${item.color};"></span>
                ${item.seriesName}：${item.value}
              </div>
            </div>
          </div>`;
        });
        return `<div style="padding: 8px; border-radius: 4px; background: rgba(255, 255, 255, 0.95); color: #333; font-size: 12px; border: 1px solid #e4e7ed;">${html}</div>`;
      }
    },
    legend: {
      data: ['电子公文处理', '电子印章处理'],
      textStyle: {
        color: '#666666',
        fontSize: 10
      },
      itemWidth: 12,
      itemHeight: 8,
      top: 0,
      right: 10
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      top: '30px',
      containLabel: true
    },
    backgroundColor: 'transparent',
    xAxis: {
      type: 'category',
      data: props.chartData.map(item => item.date),
      axisLine: {
        lineStyle: {
          color: '#e4e7ed'
        }
      },
      axisLabel: {
        color: '#666666'
      },
      axisTick: {
        show: false
      }
    },
    yAxis: {
      type: 'value',
      name: '',
      min: 0,
      max: function(value: { max: number }) {
        return Math.ceil(value.max / 100) * 100;
      },
      interval: 100,
      axisLine: {
        show: false
      },
      axisLabel: {
        color: '#666666',
        formatter: '{value}'
      },
      splitLine: {
        lineStyle: {
          color: '#e4e7ed',
          type: 'dashed'
        }
      }
    },
    series: [
      {
        name: '电子公文处理',
        type: 'bar',
        barWidth: 10,
        barGap: '30%',
        data: props.chartData.map(item => item.documentProcessing),
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: '#00BFFF' },
            { offset: 1, color: '#0080FF' }
          ])
        }
      },
      {
        name: '电子印章处理',
        type: 'bar',
        barWidth: 10,
        barGap: '30%',
        data: props.chartData.map(item => item.documentSeal),
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: '#00FF7F' },
            { offset: 1, color: '#00CD66' }
          ])
        }
      }
    ]
  };

  chartInstance.setOption(option);
};

// 处理窗口大小变化
const handleResize = () => {
  if (chartInstance) {
    chartInstance.resize();
  }
};

// 监听数据变化
watch(() => props.chartData, () => {
  updateChart();
}, { deep: true });

onMounted(() => {
  initChart();
});

onUnmounted(() => {
  if (chartInstance) {
    chartInstance.dispose();
    window.removeEventListener('resize', handleResize);
  }
});

// 导出组件
defineExpose({
  updateChart
});
</script>

<style scoped>
.document-chart {
  width: 100%;
  height: 180px;
}
</style>
