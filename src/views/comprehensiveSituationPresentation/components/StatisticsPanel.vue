<script setup lang="ts">
import { ref, onMounted } from 'vue';
import * as echarts from 'echarts';

const props = defineProps<{
  type: 'user' | 'alert';
}>();

const chartRef = ref<HTMLElement>();
let chart: echarts.ECharts;

const userOption = {
  color: ['#37a2da', '#32c5e9', '#9fe6b8', '#ffdb5c'],
  tooltip: {
    trigger: 'item'
  },
  legend: {
    top: '5%',
    left: 'center',
    textStyle: {
      color: '#fff'
    }
  },
  series: [
    {
      name: '用户类型',
      type: 'pie',
      radius: ['40%', '70%'],
      avoidLabelOverlap: false,
      itemStyle: {
        borderRadius: 10,
        borderColor: '#fff',
        borderWidth: 2
      },
      label: {
        show: false,
        position: 'center'
      },
      emphasis: {
        label: {
          show: true,
          fontSize: 40,
          fontWeight: 'bold'
        }
      },
      labelLine: {
        show: false
      },
      data: [
        { value: 1048, name: '管理员' },
        { value: 735, name: '运维人员' },
        { value: 580, name: '安全员' },
        { value: 484, name: '普通用户' }
      ]
    }
  ]
};

const alertOption = {
  color: ['#ff6b6b', '#ffd93d', '#6c5ce7', '#a8e6cf'],
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'shadow'
    }
  },
  grid: {
    left: '3%',
    right: '4%',
    bottom: '3%',
    containLabel: true
  },
  xAxis: [
    {
      type: 'category',
      data: ['高危', '中危', '低危', '提醒'],
      axisTick: {
        alignWithLabel: true
      },
      axisLabel: {
        color: '#fff'
      }
    }
  ],
  yAxis: [
    {
      type: 'value',
      axisLabel: {
        color: '#fff'
      }
    }
  ],
  series: [
    {
      name: '告警数量',
      type: 'bar',
      barWidth: '60%',
      data: [10, 52, 200, 334]
    }
  ]
};

onMounted(() => {
  if (chartRef.value) {
    chart = echarts.init(chartRef.value);
    const option = props.type === 'user' ? userOption : alertOption;
    chart.setOption(option);
  }
});
</script>

<template>
  <div class="statistics-panel">
    <div ref="chartRef" class="chart-container"></div>
  </div>
</template>

<style lang="scss" scoped>
.statistics-panel {
  .chart-container {
    width: 100%;
    height: 100%;
  }
}
</style>