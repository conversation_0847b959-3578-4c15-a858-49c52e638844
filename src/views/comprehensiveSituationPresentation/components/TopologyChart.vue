<script setup lang="ts">
import { ref, onMounted } from 'vue';
import * as echarts from 'echarts';

const chartRef = ref<HTMLElement>();
let chart: echarts.ECharts;

const nodes = [
  { name: '核心交换机', value: [400, 300], symbolSize: 70, category: 0 },
  { name: '安全网关', value: [400, 200], symbolSize: 50, category: 1 },
  { name: '防火墙', value: [400, 400], symbolSize: 50, category: 1 },
  { name: '接入交换机1', value: [200, 300], symbolSize: 40, category: 2 },
  { name: '接入交换机2', value: [600, 300], symbolSize: 40, category: 2 },
  { name: '终端1', value: [100, 200], symbolSize: 30, category: 3 },
  { name: '终端2', value: [100, 400], symbolSize: 30, category: 3 },
  { name: '终端3', value: [700, 200], symbolSize: 30, category: 3 },
  { name: '终端4', value: [700, 400], symbolSize: 30, category: 3 }
];

const links = [
  { source: '核心交换机', target: '安全网关' },
  { source: '核心交换机', target: '防火墙' },
  { source: '核心交换机', target: '接入交换机1' },
  { source: '核心交换机', target: '接入交换机2' },
  { source: '接入交换机1', target: '终端1' },
  { source: '接入交换机1', target: '终端2' },
  { source: '接入交换机2', target: '终端3' },
  { source: '接入交换机2', target: '终端4' }
];

onMounted(() => {
  if (chartRef.value) {
    chart = echarts.init(chartRef.value);
    const option = {
      backgroundColor: 'transparent',
      tooltip: {},
      legend: [
        {
          data: ['核心设备', '安全设备', '接入设备', '终端设备'],
          textStyle: { color: '#333333' }
        }
      ],
      series: [
        {
          type: 'graph',
          layout: 'none',
          symbolSize: 50,
          roam: true,
          label: {
            show: true,
            color: '#333333'
          },
          edgeSymbol: ['circle', 'arrow'],
          edgeSymbolSize: [4, 10],
          edgeLabel: {
            fontSize: 20
          },
          categories: [
            { name: '核心设备' },
            { name: '安全设备' },
            { name: '接入设备' },
            { name: '终端设备' }
          ],
          data: nodes,
          links: links,
          lineStyle: {
            opacity: 0.9,
            width: 2,
            curveness: 0
          },
          itemStyle: {
            color: function(params: any) {
              const colors = ['#37a2da', '#32c5e9', '#9fe6b8', '#ffdb5c'];
              return colors[params.data.category];
            }
          }
        }
      ]
    };
    chart.setOption(option);
  }
});
</script>

<template>
  <div class="topology-chart">
    <div ref="chartRef" class="chart-container"></div>
  </div>
</template>

<style lang="scss" scoped>
.topology-chart {
  .chart-container {
    width: 100%;
    height: 100%;
  }
}
</style>