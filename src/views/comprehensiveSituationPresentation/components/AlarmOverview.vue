<script lang="ts">
import { defineComponent } from 'vue';

export default defineComponent({
  name: 'AlarmOverview'
});
</script>

<script setup lang="ts">
import totalAlarmIcon from '../assets/总告警-图标@3x.png';
import urgentAlarmIcon from '../assets/紧急告警-图标@3x.png';
import importantAlarmIcon from '../assets/重要告警-图标@3x.png';
import alarmTitleImg from '../assets/告警概览标题.png';

// 定义数据项类型
interface AlarmItem {
  id: string;
  value: number;
  label: string;
  unit: string;
  color?: string;
}

// 定义组件接收的属性
interface Props {
  items?: AlarmItem[];
  title?: string;
}

// 设置默认值
const props = withDefaults(defineProps<Props>(), {
  items: () => [],
  title: '告警概览'
});

// 根据告警ID获取图标
const getIconById = (id: string) => {
  switch (id) {
    case 'total-alarms':
      return totalAlarmIcon;
    case 'urgent-alarms':
      return urgentAlarmIcon;
    case 'important-alarms':
      return importantAlarmIcon;
    default:
      return totalAlarmIcon;
  }
};


</script>

<template>
  <div class="alarm-overview">
    <div class="alarm-title">
      <img :src="alarmTitleImg" alt="告警概览" class="title-image" />
    </div>
    <div class="alarm-items">
      <!-- 动态生成告警数据项 -->
      <div v-for="item in props.items" :key="item.id" class="alarm-item">
        <div class="icon-container" >
          <img :src="getIconById(item.id)" class="alarm-icon" alt="" />
        </div>
        <div class="alarm-content">
          <div class="alarm-value" :style="{ color: item.color || '#00a8ff' }">
            {{ item.value }}<span class="duration">次 / {{ item.unit }}</span>
          </div>
          <div class="alarm-label">{{ item.label }}</div>
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.alarm-overview {
  position: relative;
  width: 100%;
  padding: 10px;
  background-color: rgba(255, 255, 255, 0.9);
  border-radius: 4px;
  box-sizing: border-box;
  border: 1px solid #e4e7ed;

  .alarm-title {
    margin-bottom: 15px;
    padding-left: 10px;

    .title-image {
      position: absolute;
      top: -33px;
      left: -4px;
      height: 63px;
      object-fit: contain;
      margin-top: 5px;
      margin-bottom: 5px;
    }
  }

  .alarm-items {
    display: flex;
    flex-direction: column;
    gap: 15px;
  }

  .alarm-item {
    display: flex;
    align-items: center;
    padding: 0 10px;

    .icon-container {
      width: 32px;
      height: 32px;
      border-radius: 4px;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 12px;
      flex-shrink: 0;

      .alarm-icon {
        width: 32px;
      height: 32px;
        object-fit: contain;
      }
    }

    .alarm-content {
      display: flex;
      flex-direction: column;

      .alarm-value {
        font-size: 20px;
        font-weight: bold;
        line-height: 1.2;
        text-shadow: 0 0 5px rgba(0, 168, 255, 0.3);
        font-family: 'Arial', sans-serif;

        .duration {
          font-size: 14px;
          color: #7b8794;
          opacity: 0.8;
          margin-left: 4px;
          font-weight: normal;
        }
      }

      .alarm-label {
        font-size: 14px;
        color: #2c3e50;
        opacity: 0.8;
        // margin-top: 4px;
      }
    }
  }
}
</style>
