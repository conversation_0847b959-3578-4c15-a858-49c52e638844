<script setup lang="ts">
import { defineComponent, CSSProperties } from 'vue';

// 导入图标资源
import cpuIcon from '@/views/comprehensiveSituationPresentation/assets/CPU数量-图标@3x.png';
import memoryIcon from '@/views/comprehensiveSituationPresentation/assets/内存容量-图标@3x.png';
import diskIcon from '@/views/comprehensiveSituationPresentation/assets/磁盘容量-图标@3x.png';

defineComponent({
  name: 'ResourceLoadSituation'
});

// 定义资源项的接口
interface ResourceItem {
  id: string;           // 资源项的唯一标识
  type: string;         // 资源类型，用于确定图标
  value: number;        // 资源值
  percent: number;      // 资源使用百分比
  unit?: string;        // 单位，可选
  label: string;        // 显示标签
  icon?: string;        // 自定义图标，可选
  showDecimal?: boolean; // 是否显示小数点，可选
  [key: string]: any;    // 允许其他属性
}

// 定义组件属性
const props = withDefaults(defineProps<{
  // 资源数据数组，必须传入
  items: any[];
}>(), {
  items: () => []
});

// 创建图标映射对象
const iconMap: Record<string, any> = {
  'cpu': cpuIcon,
  'memory': memoryIcon,
  'disk': diskIcon,
  // 可以根据需要添加更多的图标映射
};

// 获取图标路径
const getIconPath = (item: ResourceItem): any => {
  // 如果有自定义图标且是完整路径，直接返回
  if (item.icon && typeof item.icon === 'object') {
    return item.icon;
  }

  // 如果有自定义图标字符串，尝试从映射中获取
  if (item.icon && typeof item.icon === 'string' && iconMap[item.icon]) {
    return iconMap[item.icon];
  }

  // 否则根据类型使用默认图标
  return iconMap[item.type] || cpuIcon; // 默认使用CPU图标
};

// 格式化数字显示
const formatNumber = (num: number, showDecimal: boolean = false): string => {
  const formattedNum = num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');
  return showDecimal ? `${formattedNum}.00` : formattedNum;
};

// 计算进度条样式
const getProgressStyle = (percent: number): CSSProperties => {
  return {
    width: `${percent}%`
  };
};

// 计算进度条结尾样式
const endRectStyle = (percent: number): CSSProperties => {
  return {
    left: `${percent}%`
  };
};
</script>

<template>
  <div class="resource-load-situation">
    <!-- 动态生成资源项 -->
    <div v-for="item in props.items" :key="item.id" class="resource-item">
      <div class="resource-icon">
        <img :src="getIconPath(item)" :alt="item.label" />
      </div>
      <div class="resource-info">
        <div class="resource-header">
          <div class="resource-label">{{ item.label }}</div>
          <div class="resource-value">
            {{ formatNumber(item.value, item.showDecimal) }}
            <span v-if="item.unit" class="resource-unit">{{ item.unit }}</span>
            <span class="resource-percent">({{ item.percent }}%)</span>
          </div>
        </div>
        <div class="bar-wrap">
          <div class="bar">
            <div class="bar-percent" :style="getProgressStyle(item.percent)"></div>
          </div>
          <div class="end-rect" :style="endRectStyle(item.percent)"></div>
        </div>
      </div>
    </div>

    <!-- 当没有数据时显示的提示 -->
    <div v-if="props.items.length === 0" class="no-data">
      暂无资源负载数据
    </div>
  </div>
</template>

<style lang="scss" scoped>
.resource-load-situation {
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 15px;
  padding: 10px 0;

  .no-data {
    text-align: center;
    color: #7b8794;
    padding: 20px 0;
    font-size: 14px;
  }

  .resource-item {
    display: flex;
    align-items: center;
    gap: 10px;

    .resource-icon {
      width: 40px;
      height: 40px;
      display: flex;
      align-items: center;
      justify-content: center;

      img {
        width: 30px;
        height: 30px;
      }
    }

    .resource-info {
      flex: 1;
      display: flex;
      flex-direction: column;
      gap: 6px;

      .resource-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        width: 100%;

        .resource-label {
          font-size: 12px;
          color: #2c3e50;
          font-weight: 400;
        }

        .resource-value {
          font-size: 14px;
          font-weight: 500;
          color: #1976d2;
          display: flex;
          align-items: center;
          gap: 3px;

          .resource-unit {
            font-size: 12px;
            color: #1976d2;
            font-weight: normal;
          }

          .resource-percent {
            font-size: 12px;
            color: #7b8794;
            font-weight: normal;
          }
        }
      }

      .bar-wrap {
        width: 100%;
        position: relative;
        display: flex;
        align-items: center;
        height: 8px;

        .bar {
          width: 100%;
          height: 8px;
          background: rgba(0, 0, 0, 0.1);
          border-radius: 0px;

          .bar-percent {
            background: linear-gradient(270deg, #1976d2 0%, rgba(25, 118, 210, 0) 100%);
            height: 100%;
            border-radius: 0px;
          }
        }

        .end-rect {
          width: 3px;
          position: absolute;
          background: #ffffff;
          height: 100%;
          border-radius: 0;
        }
      }
    }
  }
}
</style>
