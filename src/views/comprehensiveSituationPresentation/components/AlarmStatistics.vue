<script lang="ts">
import { defineComponent } from 'vue';

export default defineComponent({
  name: 'AlarmStatistics'
});
</script>

<script setup lang="ts">
// 导入背景图片
import bg5 from '@/views/comprehensiveSituationPresentation/assets/<EMAIL>';
import bg02 from '@/views/comprehensiveSituationPresentation/assets/<EMAIL>';
import bg6 from '@/views/comprehensiveSituationPresentation/assets/<EMAIL>';
import bg04 from '@/views/comprehensiveSituationPresentation/assets/<EMAIL>';

// 导入图标
import alarmIcon from '@/views/comprehensiveSituationPresentation/assets/总告警-图标@3x.png';
import serverIcon from '@/views/comprehensiveSituationPresentation/assets/服务器-图标@3x.png';
import vmIcon from '@/views/comprehensiveSituationPresentation/assets/虚拟机-图标@3x.png';
import middlewareIcon from '@/views/comprehensiveSituationPresentation/assets/中间件-图标@3x.png';
import cloudIcon from '@/views/comprehensiveSituationPresentation/assets/云平台-图标@3x.png';

// 定义数据项类型
interface StatItem {
  id: string;
  label: string;
  value: number;
  icon?: string;
  type?: 'scene' | 'device';
}

// 定义组件接收的属性
interface Props {
  items?: StatItem[];
}

// 设置默认值
const props = withDefaults(defineProps<Props>(), {
  items: () => []
});
</script>

<template>
  <div class="alarm-statistics">
    <div class="alarm-title">
      <img class="title-image" src="@/views/comprehensiveSituationPresentation/assets/CPU利用率趋势图-标题@3x(6).png" alt="告警综合关联统计" />
    </div>
    <div class="stat-grid">
      <!-- 上半部分 -->
      <div class="stat-row top-row">
        <div class="stat-item" :style="{backgroundImage: `url(${bg5})`, backgroundSize: 'contain', backgroundPosition: 'center', backgroundRepeat: 'no-repeat'}">
          <div class="stat-content">
            <div class="stat-value">{{ props.items[0]?.value || 25 }}</div>
            <div class="stat-label">{{ props.items[0]?.label || '认证场景' }}</div>
          </div>
        </div>
        <div class="stat-item" :style="{backgroundImage: `url(${bg02})`, backgroundSize: 'contain', backgroundPosition: 'center', backgroundRepeat: 'no-repeat'}">
          <div class="stat-content">
            <div class="stat-value">{{ props.items[1]?.value || 32 }}</div>
            <div class="stat-label">{{ props.items[1]?.label || '通讯场景' }}</div>
          </div>
        </div>
      </div>

      <!-- 中间图标 -->
      <div class="center-icon">
        <img :src="alarmIcon" alt="总告警图标" />
      </div>

      <!-- 下半部分 -->
      <div class="stat-row bottom-row">
        <div class="stat-item" :style="{backgroundImage: `url(${bg6})`, backgroundSize: 'contain', backgroundPosition: 'center', backgroundRepeat: 'no-repeat'}">
          <div class="stat-content">
            <div class="stat-value">{{ props.items[2]?.value || 32 }}</div>
            <div class="stat-label">{{ props.items[2]?.label || '视频会议' }}</div>
          </div>
        </div>
        <div class="stat-item" :style="{backgroundImage: `url(${bg04})`, backgroundSize: 'contain', backgroundPosition: 'center', backgroundRepeat: 'no-repeat'}">
          <div class="stat-content">
            <div class="stat-value">{{ props.items[3]?.value || 32 }}</div>
            <div class="stat-label">{{ props.items[3]?.label || '视频监控' }}</div>
          </div>
        </div>
      </div>

      <!-- 底部设备统计 -->
      <div class="device-stats">
        <div class="device-row">
          <div class="device-item">
            <img :src="serverIcon" alt="服务器" />
            <span class="device-label">服务器：</span>
            <span class="device-value">{{ props.items[4]?.value || 113 }}</span>
          </div>
          <div class="device-item">
            <img :src="vmIcon" alt="虚拟机" />
            <span class="device-label">虚拟机：</span>
            <span class="device-value">{{ props.items[5]?.value || 114 }}</span>
          </div>
        </div>
        <div class="device-row">
          <div class="device-item">
            <img :src="middlewareIcon" alt="中间件" />
            <span class="device-label">中间件：</span>
            <span class="device-value">{{ props.items[6]?.value || 13 }}</span>
          </div>
          <div class="device-item">
            <img :src="cloudIcon" alt="云平台" />
            <span class="device-label">云平台：</span>
            <span class="device-value">{{ props.items[7]?.value || 4 }}</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.alarm-statistics {
  position: relative;
  width: 100%;
  height: 100%;
  padding: 10px;
  background-color: rgba(255, 255, 255, 0.9);
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;

  .alarm-title {
    margin-bottom: 15px;

    .title-image {
      position: absolute;
      top: -33px;
      left: -4px;
      height: 63px;
      object-fit: contain;
      margin-top: 5px;
      margin-bottom: 5px;
    }
  }

  .stat-grid {
    display: flex;
    flex-direction: column;
    flex: 1;
    position: relative;
  }

  .stat-row {
    display: flex;
    justify-content: space-between;
    height: 50px;
    margin-bottom: 0;
  }

  .top-row, .bottom-row {
    z-index: 1;
  }

  .center-icon {
    position: absolute;
    top: calc(50% - 29px); /* 调整垂直位置，考虑到标题的margin和底部区域 */
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 2;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #409eff;
    border-radius: 50%;
    border: 2px solid #409eff;
    box-shadow: 0 0 10px rgba(64, 158, 255, 0.3);

    img {
      width: 22px;
      height: 22px;
    }
  }

  .stat-item {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    // background-color: rgba(0, 42, 67, 0.5);
    // border: 1px solid rgba(0, 168, 255, 0.3);
    border-radius: 2px;
    margin: 0 2px;
    // height: 100%;
    background-position: center;

    .stat-content {
      display: flex;
      flex-direction: column;
      align-items: center;

      .stat-value {
        font-size: 16px;
        font-weight: bold;
        color: #409eff;
        line-height: 1;
        text-shadow: 0 0 5px rgba(64, 158, 255, 0.3);
        font-family: 'Arial', sans-serif;
      }

      .stat-label {
        font-size: 10px;
        color: #333333;
        opacity: 0.9;
        margin-top: 1px;
        text-align: center;
      }
    }
  }

  .device-stats {
    margin-top: 2px;
    background-color: rgba(255, 255, 255, 0.9);
    border: 1px solid #e4e7ed;
    border-radius: 2px;
    padding: 4px;
  }

  .device-row {
    display: flex;
    justify-content: space-between;
    margin-bottom: 2px;

    &:last-child {
      margin-bottom: 0;
    }
  }

  .device-item {
    display: flex;
    align-items: center;
    flex: 1;
    margin: 0 2px;

    img {
      width: 16px;
      height: 16px;
      margin-right: 4px;
    }

    .device-label {
      color: #409eff;
      font-size: 12px;
      margin-right: 4px;
    }

    .device-value {
      color: #333333;
      font-size: 14px;
      font-weight: bold;
    }
  }
}
</style>
