<template>
  <div class="alarm-list">
    <div class="alarm-title">
      <img class="title-image" src="@/views/comprehensiveSituationPresentation/assets/CPU利用率趋势图-标题@3x(5).png" alt="告警列表" />
    </div>
    <div class="alarm-content">
      <div class="alarm-header">
        <div class="alarm-summary">
          <span>全部: {{ totalAlarms }}</span>
          <span class="severe">
            <img :src="severeIcon" alt="严重" />
            严重: {{ severeCount }}
          </span>
          <span class="important">
            <img :src="importantIcon" alt="重要" />
            重要: {{ importantCount }}
          </span>
          <span class="minor">
            <img :src="minorIcon" alt="轻微" />
            轻微: {{ minorCount }}
          </span>
        </div>
      </div>
      <div class="alarm-table">
        <el-table
          :data="displayedAlarms"
          style="width: 100%"
          size="small"
          :header-cell-style="headerCellStyle"
          :cell-style="cellStyle"
          :row-style="rowStyle"
          :border="false"
        >
          <el-table-column prop="id" label="序号" width="60" align="center" />
          <el-table-column prop="time" label="最近发生事件" width="160" />
          <el-table-column prop="category" label="告警类别" width="100" />
          <el-table-column prop="level" label="告警级别" width="100">
            <template #default="scope">
              <span :class="getLevelClass(scope.row.level)">{{ scope.row.level }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="description" label="故障正文" show-overflow-tooltip />
        </el-table>
      </div>
      <div class="alarm-pagination">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[5, 10, 20]"
          :pager-count="5"
          layout="sizes, prev, pager, next, jumper, total"
          :total="totalAlarms"
          background
          class="is-dark"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue';
import severeIcon from '@/views/comprehensiveSituationPresentation/assets/严重告警-图标@3x.png';
import importantIcon from '@/views/comprehensiveSituationPresentation/assets/重要告警-图标@3x(1).png';
import minorIcon from '@/views/comprehensiveSituationPresentation/assets/轻微告警-图标@3x.png';

// 定义组件接收的属性
interface AlarmItem {
  id: string;
  time: string;
  category: string;
  level: string; // 使用更灵活的字符串类型
  description: string;
}

interface Props {
  items?: AlarmItem[];
  defaultPageSize?: number;
  defaultCurrentPage?: number;
}

// 设置默认值
const props = withDefaults(defineProps<Props>(), {
  items: () => [],
  defaultPageSize: 5,
  defaultCurrentPage: 1
});

// 分页相关状态
const currentPage = ref(props.defaultCurrentPage);
const pageSize = ref(props.defaultPageSize);

// 计算属性
const totalAlarms = computed(() => props.items.length);
const severeCount = computed(() => props.items.filter(item => item.level === '严重').length);
const importantCount = computed(() => props.items.filter(item => item.level === '重要').length);
const minorCount = computed(() => props.items.filter(item => item.level === '轻微').length);

// 当前页显示的数据
const displayedAlarms = computed(() => {
  const startIndex = (currentPage.value - 1) * pageSize.value;
  const endIndex = startIndex + pageSize.value;
  return props.items.slice(startIndex, endIndex);
});

// 处理页码变化
// 直接在按钮中处理页码变化，不再需要这个函数

// 分页组件会自动处理页码和每页条数的变化

// 根据告警级别获取对应的CSS类名
const getLevelClass = (level: string): string => {
  switch (level) {
    case '严重':
      return 'level-severe';
    case '重要':
      return 'level-important';
    case '轻微':
      return 'level-minor';
    default:
      return '';
  }
};

// 表格样式
const headerCellStyle = {
  backgroundColor: '#f5f7fa',
  color: '#333333',
  fontSize: '12px',
  padding: '6px 8px',
  borderBottom: '1px solid #e4e7ed',
  fontWeight: 'normal'
};

const cellStyle = {
  color: '#333333',
  fontSize: '12px',
  padding: '6px 8px',
  borderBottom: '1px solid #e4e7ed',
  backgroundColor: 'transparent'
};

const rowStyle = (_row: any, rowIndex: number) => {
  return {
    backgroundColor: rowIndex % 2 === 0 ? 'rgba(255, 255, 255, 0.9)' : 'rgba(248, 250, 252, 0.9)',
    cursor: 'default'
  };
};
</script>

<style lang="scss" scoped>
.alarm-list {
  position: relative;
  width: 100%;
  height: 100%;
  padding: 10px;
  background-color: rgba(255, 255, 255, 0.9);
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;

  .alarm-title {
    margin-bottom: 15px;

    .title-image {
      position: absolute;
      top: -33px;
      left: -4px;
      height: 63px;
      object-fit: contain;
      margin-top: 5px;
      margin-bottom: 5px;
    }
  }

  .alarm-content {
    flex: 1;
    display: flex;
    flex-direction: column;
  }

  .alarm-header {
    margin-bottom: 8px;
  }

  .alarm-summary {
    display: flex;
    align-items: center;
    font-size: 12px;
    color: #333333;

    span {
      margin-right: 15px;
      display: flex;
      align-items: center;

      img {
        width: 14px;
        height: 14px;
        margin-right: 4px;
      }
    }

    .severe {
      color: #FF3838;
    }

    .important {
      color: #FF7716;
    }

    .minor {
      color: #E4C513;
    }
  }

  .alarm-table {
    flex: 1;
    overflow: hidden;

    // 自定义Element Plus表格样式
    :deep(.el-table) {
      background-color: transparent;
      border: none;

      &::before {
        display: none;
      }

      .el-table__inner-wrapper::before {
        display: none;
      }

      .el-table__header-wrapper {
        th {
          background-color: #f5f7fa;
          border-bottom: 1px solid #e4e7ed;

          .cell {
            font-weight: normal;
            line-height: 20px;
          }
        }
      }

      .el-table__body-wrapper {
        background-color: transparent;

        td {
          border-bottom: 1px solid #e4e7ed;
        }

        .cell {
          line-height: 20px;
        }
      }

      .el-table__row {
        background-color: transparent;

        &:hover > td {
          background-color: rgba(64, 158, 255, 0.1) !important;
        }
      }

      .el-table__empty-block {
        background-color: rgba(255, 255, 255, 0.9);

        .el-table__empty-text {
          color: #333333;
        }
      }
    }

    .level-severe {
      color: #FF3838;
    }

    .level-important {
      color: #FF7716;
    }

    .level-minor {
      color: #E4C513;
    }
  }

  .alarm-pagination {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    margin-top: 8px;
    font-size: 12px;
    color: #333333;

    // 使用Element Plus的默认样式
  }
  
}
</style>
