<script setup lang="ts">
import roomIcon from '@/views/comprehensiveSituationPresentation/assets/机房-图标@3x.png'
import cabinetIcon from '@/views/comprehensiveSituationPresentation/assets/机柜-图标@3x.png'
import serverIcon from '@/views/comprehensiveSituationPresentation/assets/服务器-图标@3x.png'
import vmIcon from '@/views/comprehensiveSituationPresentation/assets/虚拟机-图标@3x.png'
import middlewareIcon from '@/views/comprehensiveSituationPresentation/assets/中间件-图标@3x.png'
import cloudIcon from '@/views/comprehensiveSituationPresentation/assets/云平台-图标@3x.png'
import appServiceIcon from '@/views/comprehensiveSituationPresentation/assets/应用服务-图标@3x.png'
import otherIcon from '@/views/comprehensiveSituationPresentation/assets/其它-图标@3x.png'


import { ref } from 'vue';

// 定义组件属性
const props = defineProps<{
  // 可以添加自定义属性，如果需要的话
}>();

// 定义组件名称
defineOptions({
  name: 'ResourceCards'
});

// 资源数据
const resourceData = ref([
  {
    id: 1,
    title: '机房',
    count: 2,
    icon: roomIcon,
    color: 'rgb(0, 168, 255)',
    bgColor: 'rgba(0, 168, 255, 0.15)'
  },
  {
    id: 2,
    title: '机柜',
    count: 2,
    icon: cabinetIcon,
    color: 'rgb(0, 168, 255)',
    bgColor: 'rgba(0, 168, 255, 0.15)'
  },
  {
    id: 3,
    title: '服务器',
    count: 113,
    icon: serverIcon,
    color: 'rgb(0, 168, 255)',
    bgColor: 'rgba(0, 168, 255, 0.15)'
  },
  {
    id: 4,
    title: '虚拟机',
    count: 114,
    icon: vmIcon,
    color: 'rgb(0, 168, 255)',
    bgColor: 'rgba(0, 168, 255, 0.15)'
  },
  {
    id: 5,
    title: '中间件',
    count: 68,
    icon: middlewareIcon,
    color: 'rgb(0, 168, 255)',
    bgColor: 'rgba(0, 168, 255, 0.15)'
  },
  {
    id: 6,
    title: '云平台',
    count: 29,
    icon: cloudIcon,
    color: 'rgb(0, 168, 255)',
    bgColor: 'rgba(0, 168, 255, 0.15)'
  },
  {
    id: 7,
    title: '应用服务',
    count: 14,
    icon: appServiceIcon,
    color: 'rgb(0, 168, 255)',
    bgColor: 'rgba(0, 168, 255, 0.15)'
  },
  {
    id: 8,
    title: '其它',
    count: 3,
    icon: otherIcon,
    color: 'rgb(0, 168, 255)',
    bgColor: 'rgba(0, 168, 255, 0.15)'
  }
]);
</script>

<template>
  <div class="resource-cards-container">
    <div class="resource-cards-grid">
      <div
        v-for="item in resourceData"
        :key="item.id"
        class="resource-card"
      >
        <div class="icon-container">
          <img
            :src="item.icon"
            class="icon-image"
          />
        </div>
        <div class="card-content">
          <div class="card-count">{{ item.count }}</div>
          <div class="card-title">{{ item.title }}</div>
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.resource-cards-container {
  width: 100%;

  .resource-cards-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    // gap: 20px;

    @media (max-width: 1400px) {
      grid-template-columns: repeat(2, 1fr);
    }

    .resource-card {
      display: flex;
      align-items: center;
      padding: 10px;
      background-color: transparent;
      transition: all 0.3s ease;

      &:hover {
        transform: translateY(-2px);
      }

      .icon-container {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 48px;
        height: 48px;
        gap: 6px;

        .icon-image {
          width: 38px;
          height: 38px;
          margin-right: 13px;
        }
      }

      .card-content {
        display: flex;
        flex-direction: column;

        .card-count {
          font-size: 20px;
          font-weight: bold;
          color: #1976d2;
          line-height: 1.2;
        }

        .card-title {
          font-size: 14px;
          color: #2c3e50;
          margin-top: 2px;
          white-space: nowrap;
        }
      }
    }
  }
}
</style>
