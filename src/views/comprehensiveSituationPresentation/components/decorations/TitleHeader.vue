<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, nextTick } from 'vue';
import type { DateModelType } from 'element-plus';
import { useFullscreen } from '@vueuse/core';
import { useSettingStoreHook } from '@/store/modules/settings';
import { emitter } from '@/utils/mitt';
import { message } from '@/utils/message';

// 导入投屏模式图标
import projectionModeIcon from '@/assets/svg/full_screen.svg';
import exitProjectionModeIcon from '@/assets/svg/exit_screen.svg';

const props = defineProps<{
  title: string;
}>();

// 全屏功能
const { isFullscreen, toggle } = useFullscreen();
const pureSetting = useSettingStoreHook();

// 是否处于投屏模式
const isProjectionMode = ref(false);

// 切换投屏模式
function toggleProjectionMode() {
  // 切换全屏
  toggle();

  // 切换内容区全屏（隐藏侧边栏）
  pureSetting.hiddenSideBar
    ? pureSetting.changeSetting({ key: 'hiddenSideBar', value: false })
    : pureSetting.changeSetting({ key: 'hiddenSideBar', value: true });

  // 切换标签栏显示状态
  pureSetting.changeSetting({ key: 'hiddenTag', value: !pureSetting.hiddenTag });

  // 触发标签视图变化事件
  emitter.emit('tagViewsChange', pureSetting.hiddenTag as unknown as string);

  // 更新投屏模式状态
  isProjectionMode.value = !isProjectionMode.value;

  // 显示提示信息
  if (isProjectionMode.value) {
    message('提示：已进入投屏模式，再次点击按钮或刷新页面可退出！', {
      showClose: true,
      duration: 5000,
      type: 'success'
    });
  }
}

// 当前日期时间
const now = new Date();
const currentDate = ref<any>('');

// 格式化当前星期
const weekDays = ['日', '一', '二', '三', '四', '五', '六'];
const currentWeekDay = computed(() => {
  return `星期${weekDays[new Date().getDay()]}`;
});

// 格式化当前时间
const currentTime = ref(formatTime(now));

// 快捷选项状态
const activeShortcut = ref('day');

// 时间格式化函数
function formatTime(date: Date): string {
  const hours = date.getHours().toString().padStart(2, '0');
  const minutes = date.getMinutes().toString().padStart(2, '0');
  const seconds = date.getSeconds().toString().padStart(2, '0');
  return `${hours}:${minutes}:${seconds}`;
}

// 格式化日期为YYYY-MM-DD格式
function formatDate(date: Date): string {
  const year = date.getFullYear();
  const month = (date.getMonth() + 1).toString().padStart(2, '0');
  const day = date.getDate().toString().padStart(2, '0');
  return `${year}-${month}-${day}`;
}

// 日期选择器值变化处理
const handleDateChange = (val: DateModelType) => {
  if (val) {
    currentDate.value = val;

    // 当用户自己选择日期时，取消所有快捷选项的active状态
    activeShortcut.value = '';

    // 更新按钮的active状态
    nextTick(() => {
      const shortcutBtns = document.querySelectorAll('.shortcut-buttons .shortcut-btn');
      shortcutBtns.forEach(btn => {
        btn.classList.remove('active');
      });
    });
  }
};

// 快捷选项点击处理
const handleShortcutClick = (type: string) => {
  activeShortcut.value = type;

  const end = new Date();
  let start = new Date();

  switch (type) {
    case 'day':
      start = new Date(Date.now() - 3600 * 1000 * 24);
      break;
    case 'week':
      start = new Date(Date.now() - 3600 * 1000 * 24 * 7);
      break;
    case 'month':
      start = new Date(Date.now() - 3600 * 1000 * 24 * 30);
      break;
  }

  currentDate.value = [start, end];

  // 更新按钮的active状态
  nextTick(() => {
    const shortcutBtns = document.querySelectorAll('.shortcut-buttons .shortcut-btn');
    shortcutBtns.forEach(btn => {
      const btnText = btn.textContent;
      if (
        (type === 'day' && btnText === '近一天') ||
        (type === 'week' && btnText === '近一周') ||
        (type === 'month' && btnText === '近一月')
      ) {
        btn.classList.add('active');
      } else {
        btn.classList.remove('active');
      }
    });
  });
};

// 添加快捷选项按钮
const addShortcutButtons = () => {
  nextTick(() => {
    // 获取日期选择器弹出层
    const poppers = document.querySelectorAll('.el-picker__popper');

    poppers.forEach(popper => {
      // 检查是否已经添加了快捷选项按钮
      if (!popper.querySelector('.shortcut-buttons')) {
        const bodyWrapper = popper.querySelector('.el-picker-panel__body-wrapper');

        if (bodyWrapper) {
          // 创建快捷选项按钮容器
          const shortcutButtons = document.createElement('div');
          shortcutButtons.className = 'shortcut-buttons';

          // 创建快捷选项按钮
          const dayBtn = document.createElement('div');
          dayBtn.className = `shortcut-btn ${activeShortcut.value === 'day' ? 'active' : ''}`;
          dayBtn.textContent = '近一天';
          dayBtn.addEventListener('click', () => handleShortcutClick('day'));

          const weekBtn = document.createElement('div');
          weekBtn.className = `shortcut-btn ${activeShortcut.value === 'week' ? 'active' : ''}`;
          weekBtn.textContent = '近一周';
          weekBtn.addEventListener('click', () => handleShortcutClick('week'));

          const monthBtn = document.createElement('div');
          monthBtn.className = `shortcut-btn ${activeShortcut.value === 'month' ? 'active' : ''}`;
          monthBtn.textContent = '近一月';
          monthBtn.addEventListener('click', () => handleShortcutClick('month'));

          // 添加按钮到容器
          shortcutButtons.appendChild(dayBtn);
          shortcutButtons.appendChild(weekBtn);
          shortcutButtons.appendChild(monthBtn);

          // 添加容器到日期选择器
          bodyWrapper.appendChild(shortcutButtons);
        }
      }
    });
  });
};

// 监听日期选择器的点击事件
const setupDatePickerListener = () => {
  const datePickerEl = document.querySelector('.custom-date-picker');
  if (datePickerEl) {
    datePickerEl.addEventListener('click', addShortcutButtons);
  }
};

// 定时更新时间
let timer: number;

onMounted(() => {
  timer = window.setInterval(() => {
    const now = new Date();
    currentTime.value = formatTime(now);
  }, 1000);

  // 设置默认日期范围
  handleShortcutClick('day');

  // 设置日期选择器监听器
  setupDatePickerListener();
});

onUnmounted(() => {
  clearInterval(timer);

  // 移除日期选择器监听器
  const datePickerEl = document.querySelector('.custom-date-picker');
  if (datePickerEl) {
    datePickerEl.removeEventListener('click', addShortcutButtons);
  }
});
</script>

<template>
  <div class="title-header">
    <!-- 投屏模式按钮 -->
    <div class="fullscreen-btn" @click="toggleProjectionMode" :title="isProjectionMode ? '退出投屏模式' : '进入投屏模式'">
      <img v-if="isProjectionMode" :src="exitProjectionModeIcon" alt="退出投屏模式" />
      <img v-else :src="projectionModeIcon" alt="进入投屏模式" />
    </div>
    <div class="title-content">


      <div class="title-text">{{ title }}</div>
    </div>
    <div class="time-info">
      <!-- 时间选择器 -->
      <div class="date-picker-container">
        <span class="time-label">时间：</span>
        <div class="date-picker-wrapper">
          <el-date-picker popper-class="titleHeader-date-picker-popper" v-model="currentDate" type="daterange"
            range-separator="-" start-placeholder="起始时间" end-placeholder="结束时间" :clearable="false"
            @change="handleDateChange" class="custom-date-picker" :shortcuts="[
              { text: '近一天', value: () => [new Date(Date.now() - 3600 * 1000 * 24), new Date()] },
              { text: '近一周', value: () => [new Date(Date.now() - 3600 * 1000 * 24 * 7), new Date()] },
              { text: '近一月', value: () => [new Date(Date.now() - 3600 * 1000 * 24 * 30), new Date()] }
            ]" />
        </div>
      </div>

      <!-- 当前时间显示 -->
      <div class="current-time-display">
        <div class="date-time-container">
          {{ formatDate(new Date()) }}
          <span class="week-day">{{ currentWeekDay }}</span>
          {{ currentTime }}
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
/* 投屏模式下的样式调整 */
:deep(:fullscreen) {
  width: 100vw !important;
  height: 100vh !important;
  overflow: auto;
  background-color: #f5f7fa;

  .title-header {
    position: relative;
    width: 100% !important;
  }

  .main-content {
    width: 100% !important;
    height: calc(100vh - 66px) !important;
    display: flex;
    flex-direction: column;
  }

  /* 确保所有子元素也能自适应 */
  * {
    max-width: 100%;
  }

  /* 投屏模式下的其他元素调整 */
  .time-info {
    transform: scale(0.93);
    right: 10px;
  }

  /* 投屏模式下的按钮样式 */
  .fullscreen-btn {
    background: rgba(255, 255, 255, 0.9);
    border-color: rgba(64, 158, 255, 0.7);
    box-shadow: 0 0 12px rgba(64, 158, 255, 0.2);

    &:hover {
      background: rgba(255, 255, 255, 1);
      border-color: rgba(64, 158, 255, 1);
      box-shadow: 0 0 16px rgba(64, 158, 255, 0.3);
    }
  }

  /* 确保图表和其他组件自适应 */
  .el-row,
  .el-col,
  .chart-container,
  .card-panel {
    width: 100% !important;
    max-width: 100% !important;
  }

  /* 响应式布局调整 */
  @media screen and (max-width: 1200px) {
    .title-text {
      font-size: 28px;
      letter-spacing: 6px;
    }

    .time-info {
      transform: scale(0.85);
      right: 5px;
    }
  }

  @media screen and (max-width: 768px) {
    .title-text {
      font-size: 24px;
      letter-spacing: 4px;
    }

    .time-info {
      transform: scale(0.75);
      right: 0;
      gap: 5px;
    }

    .date-picker-container {
      padding: 2px 5px;
    }

    .date-picker-wrapper {
      width: 250px;
    }
  }
}

.title-header {
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 66px;
  background-image: url('../../assets/title_background.png');
  background-size: 100% 100%;
  background-repeat: no-repeat;

  .title-content {
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    max-width: 1200px;
    padding: 0 20px;
  }

  .fullscreen-btn {
    position: absolute;
    left: 1rem;
    top: 1.5rem;

    transform: translateY(-50%);
    display: flex;
    justify-content: center;
    align-items: center;
    width: 36px;
    height: 36px;
    cursor: pointer;
    background: rgba(255, 255, 255, 0.9);
    border: 1px solid rgba(64, 158, 255, 0.5);
    border-radius: 4px;
    transition: all 0.3s;
    z-index: 10;
    /* 确保按钮始终可见 */
    box-shadow: 0 0 8px rgba(64, 158, 255, 0.2);

    &:hover {
      background: rgba(255, 255, 255, 1);
      border-color: rgba(64, 158, 255, 0.8);
      box-shadow: 0 0 12px rgba(64, 158, 255, 0.3);
    }

    &:active {
      transform: translateY(-50%) scale(0.95);
    }

    img {
      width: 20px;
      height: 20px;
      filter: invert(20%) sepia(60%) saturate(5000%) hue-rotate(200deg) brightness(105%) contrast(105%);
      transition: all 0.3s;
    }

    &:hover img {
      transform: scale(1.1);
    }

    /* 响应式调整 */
    @media screen and (max-width: 768px) {
      left: 10px;
      width: 32px;
      height: 32px;

      img {
        width: 18px;
        height: 18px;
      }
    }
  }

  .title-text {
    font-size: 33px;
    font-weight: 900;
    color: #1976d2;
    text-shadow: 0 2px 4px rgba(25, 118, 210, 0.2);
    font-style: italic;
    text-align: center;
    font-family: YouSheBiaoTiHei;
    letter-spacing: 10px;
  }

  .time-info {
    position: absolute;
    right: -16px;
    display: flex;
    align-items: center;
    gap: 15px;
    top: 0.56rem;
    transform: scale(0.93);

    .date-picker-container {
      display: flex;
      align-items: center;
      background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 250, 252, 0.95) 100%);
      border: 1px solid rgba(25, 118, 210, 0.3);
      border-radius: 6px;
      padding: 4px 10px;
      height: 32px;
      box-shadow: 0 2px 8px rgba(25, 118, 210, 0.1);

      .time-label {
        color: #1976d2;
        font-size: 14px;
        font-weight: 600;
        margin-right: 5px;
      }

      .date-picker-wrapper {
        width: 300px;
      }
    }

    .current-time-display {
      background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 250, 252, 0.95) 100%);
      border: 1px solid rgba(25, 118, 210, 0.2);
      border-radius: 6px;
      padding: 4px 10px;
      height: 32px;
      display: flex;
      align-items: center;
      box-shadow: 0 2px 8px rgba(25, 118, 210, 0.1);

      .date-time-container {
        color: #1976d2;
        font-size: 14px;
        font-weight: 600;
        display: flex;
        align-items: center;
        width: 133px;
        flex-wrap: wrap;
        justify-content: center;

        .week-day {
          margin: 0 5px;
          color: #42a5f5;
        }
      }
    }
  }
}

// 深度自定义Element Plus的DatePicker组件样式
:deep(.custom-date-picker) {
  --el-text-color-regular: #1976d2;
  --el-border-color: transparent;
  --el-fill-color-blank: transparent;
  --el-input-bg-color: transparent;
  --el-input-border-color: transparent;
  --el-input-hover-border-color: transparent;
  --el-input-focus-border-color: transparent;

  .el-input__wrapper {
    background-color: transparent;
    box-shadow: none !important;
    padding: 0;
  }

  .el-input__inner {
    color: #1976d2;
    font-weight: bold;
    height: 24px;
    font-size: 14px;
  }

  .el-range-separator {
    color: #1976d2;
  }

  .el-icon {
    color: #1976d2;
  }
}


// 日期选择器下拉面板样式
:deep(.el-picker__popper) {
  --el-bg-color: #ffffff;
  --el-border-color-light: #d9e4f5;
  --el-text-color-regular: #2c3e50;
  --el-color-primary: #1976d2;
  --el-color-primary-light-3: #42a5f5;
  --el-color-primary-light-7: #90caf9;
  --el-color-primary-light-8: #bbdefb;
  --el-color-primary-light-9: rgba(25, 118, 210, 0.2);
  --el-fill-color-blank: transparent;
  --el-border-color: #d9e4f5;
  --el-border-color-hover: #d9e4f5;
  --el-text-color-placeholder: #7b8794;
  --el-disabled-bg-color: #ffffff;
  --el-disabled-text-color: #7b8794;
  --el-disabled-border-color: #d9e4f5;

  // 弹出层样式
  border: 1px solid #d9e4f5 !important;
  box-shadow: 0 4px 12px rgba(25, 118, 210, 0.15) !important;

  .el-picker-panel {
    background-color: #ffffff;
    border: none;
    color: #2c3e50;
  }

  // 快捷选项区域
  .el-picker-panel__sidebar {
    display: none; // 隐藏原来的快捷选项区域
  }

  // 自定义快捷选项区域
  .el-picker-panel__body-wrapper {
    position: relative;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 40px;
      background-color: #0d1117;
      border-bottom: 1px solid #1a2433;
      z-index: 1;
    }
  }

  // 添加自定义快捷选项
  .el-date-range-picker__header {
    position: relative;
  }

  .el-date-range-picker__header::before {
    content: '';
    position: absolute;
    top: -40px;
    left: 0;
    width: 100%;
    height: 40px;
    display: flex;
    z-index: 2;
  }

  // 快捷选项按钮
  .el-date-range-picker__header::after {
    content: '';
    position: absolute;
    top: -40px;
    left: 0;
    width: 100%;
    height: 40px;
    z-index: 3;
  }

  // 添加快捷选项按钮
  .el-picker-panel__body-wrapper .shortcut-buttons {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 40px;
    display: flex;
    align-items: center;
    padding-left: 20px;
    z-index: 10;

    .shortcut-btn {
      color: #fff;
      font-size: 14px;
      margin-right: 20px;
      cursor: pointer;
      position: relative;
      padding: 5px 0;

      &:hover {
        color: #4080ff;
      }

      &.active {
        color: #4080ff;

        &::after {
          content: '';
          position: absolute;
          bottom: 0;
          left: 0;
          width: 100%;
          height: 2px;
          background-color: #4080ff;
        }
      }
    }
  }

  // 快捷选项样式
  .el-picker-panel__shortcut {
    color: #fff;
    padding: 8px 12px;
    font-size: 14px;

    &:hover {
      background-color: rgba(64, 128, 255, 0.2);
      color: #4080ff;
    }
  }

  // 日历头部
  .el-date-picker__header {
    margin: 45px 12px 8px;

    .el-date-picker__header-label {
      color: #fff;
      font-size: 16px;
      font-weight: bold;
    }

    .el-picker-panel__icon-btn {
      color: #fff;

      &:hover {
        color: #4080ff;
      }
    }
  }

  // 星期行
  .el-date-table th {
    color: #909399;
    font-weight: normal;
    padding: 8px 0;
    border-bottom: 1px solid #1a2433;
  }

  // 日期单元格
  .el-date-table td {
    padding: 4px 0;
    height: 36px;

    span {
      width: 32px;
      height: 32px;
      line-height: 32px;
      border-radius: 4px;

      &:hover {
        background-color: rgba(64, 128, 255, 0.2);
      }
    }

    &.available span {
      color: #fff;
    }

    &.prev-month span,
    &.next-month span {
      color: #606266;
    }

    &.today span {
      color: #4080ff;
      font-weight: bold;
    }

    &.current:not(.disabled) span {
      background-color: #4080ff;
      color: #fff;
    }

    &.in-range div {
      background-color: rgba(64, 128, 255, 0.2);
    }

    &.start-date div,
    &.end-date div {
      background-color: #4080ff;
      color: #fff;
    }

    &.selected div {
      background-color: #4080ff;
      color: #fff;
    }

    // 选中样式
    &.selected span {
      background-color: #4080ff;
      color: #fff;
    }

    // 范围选择样式
    &.in-range div {
      background-color: rgba(64, 128, 255, 0.2);
    }

    &.start-date span,
    &.end-date span {
      background-color: #4080ff;
      color: #fff;
    }
  }

  // 底部按钮区域
  .el-picker-panel__footer {
    background-color: #0d1117;
    border-top: 1px solid #1a2433;
    padding: 8px 12px;
  }

  // 按钮样式
  .el-button {
    color: #fff;
    border-color: #1a2433;
    background: transparent;

    &:hover {
      color: #4080ff;
      border-color: #4080ff;
      background-color: rgba(64, 128, 255, 0.1);
    }
  }

  .el-button--primary {
    color: #fff;
    background-color: #4080ff;
    border-color: #4080ff;

    &:hover {
      color: #fff;
      background-color: rgba(64, 128, 255, 0.8);
      border-color: rgba(64, 128, 255, 0.8);
    }
  }

  // 日期范围选择器特殊样式
  .el-date-range-picker__content {
    .el-date-range-picker__header div {
      color: #fff;
      font-weight: bold;
    }

    .is-left {
      border-right: 1px solid #1a2433;
    }
  }
}
</style>
<style lang="scss">
.titleHeader-date-picker-popper {
  background-color: #ffffff !important;
  border: 1px solid #d9e4f5 !important;
  box-shadow: 0 4px 12px rgba(25, 118, 210, 0.15) !important;

  // 弹出层箭头样式
  .el-popper__arrow::before {
    background-color: #ffffff !important;
    border-color: #d9e4f5 !important;
  }

  // 设置CSS变量
  --el-bg-color: #ffffff !important;
  --el-border-color-light: #d9e4f5 !important;
  --el-text-color-regular: #2c3e50 !important;
  --el-color-primary: #1976d2 !important;
  --el-color-primary-light-3: #42a5f5 !important;
  --el-color-primary-light-7: #90caf9 !important;
  --el-color-primary-light-8: #bbdefb !important;
  --el-color-primary-light-9: rgba(25, 118, 210, 0.2) !important;
  --el-fill-color-blank: transparent !important;
  --el-border-color: #d9e4f5 !important;
  --el-border-color-hover: #d9e4f5 !important;
  --el-text-color-placeholder: #7b8794 !important;
  --el-disabled-bg-color: #ffffff !important;
  --el-disabled-text-color: #7b8794 !important;
  --el-disabled-border-color: #d9e4f5 !important;

  // 弹出层样式
  .el-picker-panel {
    background-color: #ffffff !important;
    border: none !important;
    color: #2c3e50 !important;
  }

  // 快捷选项区域
  .el-picker-panel__sidebar {
    display: none; // 隐藏原来的快捷选项区域
  }

  .el-picker-panel__body {
    margin-left: 0 !important;
    padding: 6px !important;
  }

  // 自定义快捷选项区域
  .el-picker-panel__body-wrapper {
    position: relative;
    background-color: #ffffff !important;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 40px;
      background-color: #f8fafc !important;
      border-bottom: 1px solid #d9e4f5 !important;
      z-index: 1;
    }
  }

  // 添加自定义快捷选项
  .el-date-range-picker__header {
    position: relative;
    background-color: #0E121B !important;
  }

  // 快捷选项按钮
  .el-picker-panel__body-wrapper .shortcut-buttons {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 40px;
    display: flex;
    align-items: center;
    padding-left: 20px;
    z-index: 10;
    background-color: #f8fafc !important;

    .shortcut-btn {
      color: #2c3e50;
      font-size: 14px;
      margin-right: 20px;
      cursor: pointer;
      position: relative;
      padding: 5px 0;

      &:hover {
        color: #1976d2;
      }

      &.active {
        color: #1976d2;

        &::after {
          content: '';
          position: absolute;
          bottom: 0;
          left: 0;
          width: 100%;
          height: 2px;
          background-color: #1976d2;
        }
      }
    }
  }

  // 日历头部
  .el-date-picker__header,
  .el-date-range-picker__header {
    margin: 45px 0 8px;
    padding: 0;
    background-color: #ffffff !important;
    text-align: center !important;
    width: 100% !important;
    box-sizing: border-box !important;

    .el-date-picker__header-label,
    div {
      color: #2c3e50 !important;
      font-size: 16px !important;
      font-weight: bold !important;
    }

    .el-picker-panel__icon-btn {
      color: #2c3e50 !important;
      font-size: 14px !important;

      &:hover {
        color: #1976d2 !important;
      }
    }
  }

  // 星期行
  .el-date-table th {
    color: #7b8794 !important;
    font-weight: normal !important;
    padding: 8px 0 !important;
    border-bottom: 1px solid #d9e4f5 !important;
    background-color: #ffffff !important;
  }

  // 日期单元格
  .el-date-table td {
    padding: 2px 0 !important;
    height: 32px !important;
    background-color: #ffffff !important;

    span {
      width: 28px !important;
      height: 28px !important;
      line-height: 28px !important;
      border-radius: 4px !important;
      font-size: 13px !important;

      &:hover {
        background-color: rgba(25, 118, 210, 0.1) !important;
      }
    }

    &.available span {
      color: #2c3e50 !important;
    }

    &.prev-month span,
    &.next-month span {
      color: #7b8794 !important;
    }

    &.today span {
      color: #1976d2 !important;
      font-weight: bold !important;
    }

    &.current:not(.disabled) span {
      background-color: #1976d2 !important;
      color: #fff !important;
    }

    // 选中样式
    &.selected span {
      background-color: #1976d2 !important;
      color: #fff !important;
    }

    // 范围选择样式 - 范围内的日期
    &.in-range div {
      background-color: rgba(25, 118, 210, 0.15) !important;
    }

    // 范围选择样式 - 范围内的整行
    &.in-range {
      background-color: rgba(25, 118, 210, 0.08) !important;
    }

    // 整行选中样式
    &.in-range:not(.start-date):not(.end-date) {
      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: -1px;
        right: -1px;
        bottom: 0;
        background-color: rgba(25, 118, 210, 0.08) !important;
        z-index: -1;
      }
    }

    // 范围选择样式 - 范围的开始和结束日期
    &.start-date span,
    &.end-date span {
      background-color: #1976d2 !important;
      color: #fff !important;
    }

    // 范围选择样式 - 范围的开始和结束日期所在的行
    &.start-date,
    &.end-date {
      background-color: rgba(25, 118, 210, 0.15) !important;
    }

    // 范围选择样式 - 整行选中
    &.in-range.start-date,
    &.in-range.end-date {
      background-color: rgba(25, 118, 210, 0.15) !important;
    }
  }

  // 底部按钮区域
  .el-picker-panel__footer {
    background-color: #f8fafc !important;
    border-top: 1px solid #d9e4f5 !important;
    padding: 8px 12px !important;
  }

  // 按钮样式
  .el-button {
    color: #2c3e50 !important;
    border-color: #d9e4f5 !important;
    background: transparent !important;

    &:hover {
      color: #1976d2 !important;
      border-color: #1976d2 !important;
      background-color: rgba(25, 118, 210, 0.1) !important;
    }
  }

  .el-button--primary {
    color: #fff !important;
    background-color: #1976d2 !important;
    border-color: #1976d2 !important;

    &:hover {
      color: #fff !important;
      background-color: rgba(25, 118, 210, 0.8) !important;
      border-color: rgba(25, 118, 210, 0.8) !important;
    }
  }

  // 日期范围选择器特殊样式
  .el-date-range-picker__content {
    background-color: #ffffff !important;

    .el-date-range-picker__header {
      background-color: #ffffff !important;

      div {
        color: #2c3e50 !important;
        font-weight: bold !important;
      }
    }

    .is-left {
      border-right: 1px solid #d9e4f5 !important;
      background-color: #ffffff !important;
    }

    .is-right {
      background-color: #ffffff !important;
    }

    .el-date-table {
      background-color: #ffffff !important;
      margin: 0 !important;
      padding: 0 !important;
      width: 100% !important;
      border-spacing: 0 !important;
      border-collapse: collapse !important;
      table-layout: fixed !important;
    }
  }

  // 调整日期范围选择器的宽度
  &.el-date-range-picker {
    width: 580px !important;
    background-color: #ffffff !important;
    padding: 0 !important;

    .el-picker-panel__body {
      min-width: 580px !important;
      background-color: #ffffff !important;
      padding: 0 !important;
      margin: 0 !important;
    }

    .el-picker-panel__content {
      padding: 0 !important;
      margin: 0 !important;
      width: 100% !important;
    }

    .el-date-range-picker__content {
      padding: 0 !important;
      margin: 0 !important;
      width: 100% !important;
    }

    .el-date-range-picker__content.is-left,
    .el-date-range-picker__content.is-right {
      padding: 0 !important;
      margin: 0 !important;
      width: 50% !important;
      box-sizing: border-box !important;
    }

    // 强制移除左侧空白
    .el-date-range-picker__body,
    .el-picker-panel__body-wrapper,
    .el-picker-panel__content {
      padding: 0 !important;
      margin: 0 !important;
      width: 100% !important;
    }
  }

  /* 全局样式覆盖 */
  .el-picker-panel,
  .el-date-picker,
  .el-date-range-picker,
  .el-date-table,
  .el-date-table td,
  .el-date-table th,
  .el-date-range-picker__content,
  .el-date-range-picker__header,
  .el-picker-panel__body,
  .el-picker-panel__body-wrapper,
  .el-date-range-picker__content .is-left,
  .el-date-range-picker__content .is-right,
  .el-date-range-picker__body,
  .el-picker-panel__content {
    background-color: #ffffff !important;
  }

  /* 强制移除所有内边距和外边距 */
  .el-date-range-picker__body,
  .el-picker-panel__content,
  .el-date-range-picker__content,
  .el-date-range-picker__content.is-left,
  .el-date-range-picker__content.is-right {
    padding: 0 !important;
    margin: 0 !important;
  }

  /* 弹出层的定位和层级 */
  &.el-popper {
    z-index: 2000 !important;
  }

  /* 范围选择样式 - 整行选中 */
  .el-date-table tr {
    margin: 0 !important;
    padding: 0 !important;

    &:has(td.in-range) {
      background-color: rgba(25, 118, 210, 0.05) !important;
    }

    &:has(td.start-date),
    &:has(td.end-date) {
      background-color: rgba(25, 118, 210, 0.1) !important;
    }
  }

  /* 调整日期表格的宽度和内边距 */
  .el-date-table {
    width: 100% !important;
    border-spacing: 0 !important;
    border-collapse: collapse !important;
    table-layout: fixed !important;
    margin: 0 !important;
    padding: 0 !important;
  }

  /* 调整单元格的宽度 */
  .el-date-table th,
  .el-date-table td {
    width: calc(100% / 7) !important;
    box-sizing: border-box !important;
    padding: 2px 0 !important;
    text-align: center !important;
  }

  /* 调整日期单元格的内容居中 */
  .el-date-table td .cell {
    width: 100% !important;
    margin: 0 auto !important;
    padding: 0 !important;
    text-align: center !important;
  }

  /* 调整日期单元格的span元素居中 */
  .el-date-table td span {
    margin: 0 auto !important;
    display: inline-block !important;
  }
}
</style>