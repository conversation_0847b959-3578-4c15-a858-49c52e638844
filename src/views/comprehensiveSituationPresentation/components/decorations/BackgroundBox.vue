<script setup lang="ts">
// 背景装饰组件
</script>

<template>
  <div class="background-box">
    <div class="top-border"></div>
    <div class="content">
      <slot></slot>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.background-box {
  position: relative;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg,
          #ffffff 0%,
          #f8fafc 25%,
          #e3f2fd 50%,
          #f0f8ff 75%,
          #ffffff 100%);
  overflow: hidden;

  .top-border {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 6px;
    background: linear-gradient(90deg,
            #1976d2 0%,
            #42a5f5 25%,
            #64b5f6 50%,
            #42a5f5 75%,
            #1976d2 100%);
    box-shadow:
            0 2px 8px rgba(25, 118, 210, 0.2),
            0 0 15px rgba(66, 165, 245, 0.15);
    z-index: 1;
  }

  .content {
    position: relative;
    width: 100%;
    height: 100%;
    z-index: 0;
  }
}
</style>