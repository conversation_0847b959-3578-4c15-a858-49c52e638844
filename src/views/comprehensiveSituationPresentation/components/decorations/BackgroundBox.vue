<script setup lang="ts">
// 背景装饰组件
</script>

<template>
  <div class="background-box">
    <div class="top-border"></div>
    <div class="content">
      <slot></slot>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.background-box {
  position: relative;
  width: 100%;
  height: 100%;
  
  overflow: hidden;

  .top-border {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 6px;
    
    
    z-index: 1;
  }

  .content {
    position: relative;
    width: 100%;
    height: 100%;
    z-index: 0;
  }
}
</style>