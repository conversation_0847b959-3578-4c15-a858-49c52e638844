<script setup lang="ts">
// 背景装饰组件
</script>

<template>
  <div class="background-box">
    <div class="top-border"></div>
    <div class="content">
      <slot></slot>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.background-box {
  position: relative;
  width: 100%;
  height: 100%;
  background-color: #f5f7fa;
  overflow: hidden;

  .top-border {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #409eff 0%, #67c23a 50%, #409eff 100%);
    box-shadow: 0 0 10px rgba(64, 158, 255, 0.3);
    z-index: 1;
  }

  .content {
    position: relative;
    width: 100%;
    height: 100%;
    z-index: 0;
  }
}
</style>