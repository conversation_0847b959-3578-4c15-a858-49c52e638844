<template>
  <div class="table-top5">
    <div class="col-row">
      <div
        class="col-item"
        v-for="(column, index) in columns"
        :class="{
          'topn-col': index == 0,
          'label-col': index == 1,
          'count-col': index == 2
        }"
        :key="index"
      >
        {{ column.label }}
      </div>
    </div>

    <div class="data-rows">
      <div class="data-row" v-for="(item, index) in data || []" :key="index">
        <div class="topn-col data-item">
          <div :class="['topn-bg', 'topn-bg-' + index]" :style="bgStyle(index)">
            {{ index + 1 }}
          </div>
        </div>
        <div class="label-col bar-col data-item">
          <div class="label" :title="item.label">{{ item.label }}</div>
          <div class="bar-wrap" :style="barWrapStyle">
            <div class="bar" :style="barItemStyle()">
              <div
                class="bar-percent"
                :class="['bar-percent-' + index]"
                :style="barPercentStyle(item)"
              ></div>
            </div>
            <div class="end-rect" :style="endRectStyle(item)"></div>
          </div>
        </div>
        <div class="count-col data-item flex items-center">
          {{ item.value }}
        </div>
      </div>
    </div>

    <div class="empty-desc" v-if="(data || []).length == 0">暂无数据</div>
  </div>
</template>
<script setup lang="ts">
import { PropType, CSSProperties, computed } from "vue";
// import TOP5_bg from "./TOP5_bg.png";
defineProps({
  columns: Array as PropType<any[]>,
  data: {
    type: Array as PropType<any[]>,
    default: () => {
      return [];
    }
  }
});

const barWrapStyle = computed((): CSSProperties => {
  return {
    height: `12px`
  };
});

const bgStyle = (index): CSSProperties => {
  if (index > 4) {
    return {
      backgroundColor: `#0095ff`
    };
  }
  return {};
};

const barItemStyle = (): CSSProperties => {
  return {
    height: `8px`
  };
};
const barPercentStyle = (item): CSSProperties => {
  return {
    width: `${Math.min(item.percent || 0, 100)}%`
  };
};
const endRectStyle = (item): CSSProperties => {
  return {
    left: `${Math.min(item.percent || 0, 100)}%`
  };
};
</script>
<style scoped lang="scss">
.table-top5 {
  width: 100%;
  min-height: 265px;
  display: flex;
  flex-direction: column;
  .col-row {
    display: flex;
    margin: 10px 0 10px 0;
    .col-item {
      font-family: MiSans, MiSans;
      font-weight: 400;
      font-size: 14px;
      color: #2c3e50;
      line-height: 14px;
      display: flex;
      align-items: center;
    }
  }
  .data-rows {
    max-height: 220px;
    overflow: auto;
    &::-webkit-scrollbar {
      display: none;
    }
    &::-webkit-scrollbar-track {
      display: none;
    }
  }
  .data-row {
    display: flex;
    align-items: center;
    margin-bottom: 18px;
    .count-col {
      background-image: url("./valueBg.png");
      font-family: DIN, DIN;
      font-weight: 500;
      font-size: 16px;
      color: #1976d2;
      line-height: 16px;
      padding-left: 12px;
    }
    .data-item {
      display: flex;
    }
    .bar-col {
      display: flex;
      flex-direction: column;
      align-items: flex-start !important;
      justify-content: space-between;
      .label {
        font-family: MiSans, MiSans;
        font-weight: 400;
        font-size: 14px;
        color: #2c3e50;
        line-height: 14px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        width: 100%;
        padding-bottom: 1rem;
      }
      .bar-wrap {
        width: 100%;
        position: relative;
        display: flex;
        align-items: center;
        margin-top: 4px;
        .bar {
          width: 100%;
          background: rgba(0, 0, 0, 0.1);
          border-radius: 0px;
          .bar-percent {
            background: linear-gradient(270deg, #0066ff 0%, rgba(0,102,255,0) 100%);
            height: 100%;
            border-radius: 0px;
            &.bar-percent-0 {
              background: linear-gradient(270deg, #FF3838 0%, rgba(255,56,56,0) 100%);
            }
            &.bar-percent-1 {
              background: linear-gradient(270deg, #FF7716 0%, rgba(255,119,22,0) 100%);
            }
            &.bar-percent-2 {
              background: linear-gradient(270deg, #E4C513 0%, rgba(228,197,19,0) 100%);
            }
            &.bar-percent-3 {
              background: linear-gradient(270deg, #0095FF 0%, rgba(0,149,255,0) 100%);
            }
            &.bar-percent-4 {
              background: linear-gradient(270deg, #0095FF 0%, rgba(0,149,255,0) 100%);
            }
          }
        }
        .end-rect {
          width: 3px;
          position: absolute;
          background: #333333;
          height: 100%;
          border-radius: 0;
        }
      }
    }
  }
  .topn-col {
    width: 60px;
    height: 26px;
    justify-content: center;
  }
  .label-col {
    width: calc(100% - 136px);
    height: 26px;
    padding: 0 10px 0 20px;
  }
  .count-col {
    width: 76px;
    height: 26px;
  }
  .topn-bg {
    margin-top: 5px;
    width: 60px;
    height: 24px;
    font-family: MiSans, MiSans;
    font-weight: 500;
    font-size: 14px;
    color: #ffffff;
    line-height: 14px;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }
  .topn-bg-0 {
    background: linear-gradient(90deg, #FF3838 0%, rgba(255,56,56,0.1) 100%);
    border-radius: 2px;
  }
  .topn-bg-1 {
    background: linear-gradient(90deg, #FF7716 0%, rgba(255,119,22,0.1) 100%);
    border-radius: 2px;
  }
  .topn-bg-2 {
    background: linear-gradient(90deg, #E4C513 0%, rgba(228,197,19,0.1) 100%);
    border-radius: 2px;
  }
  .topn-bg-3 {
    background: linear-gradient(90deg, #0095FF 0%, rgba(0,149,255,0.1) 100%);
    border-radius: 2px;
  }
  .topn-bg-4 {
    background: linear-gradient(90deg, #0095FF 0%, rgba(0,149,255,0.1) 100%);
    border-radius: 2px;
  }

  .empty-desc {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    font-family: MiSans, MiSans;
    font-weight: 400;
    font-size: 14px;
    margin-bottom: 40px;
    color: #666666;
    opacity: 0.5;
  }
}
</style>
