<script setup lang="ts">
import { defineComponent, ref, onMounted, onBeforeUnmount, watch, computed } from 'vue';
import * as echarts from 'echarts/core';
import { LineChart } from 'echarts/charts';
import {
  GridComponent,
  TooltipComponent,
  TitleComponent,
  LegendComponent
} from 'echarts/components';
import { CanvasRenderer } from 'echarts/renderers';

// 注册必要的组件
echarts.use([
  LineChart,
  GridComponent,
  TooltipComponent,
  TitleComponent,
  LegendComponent,
  CanvasRenderer
]);

defineComponent({
  name: 'ResourceUsageChart'
});

// 定义图表数据接口
interface ChartDataItem {
  date: string;
  value: number;
}

// 定义图表配置接口
interface ChartConfig {
  id: string;
  title?: string;
  color: string;
  data: ChartDataItem[];
  titleImage?: string; // 添加标题图片属性
}

// 定义组件属性
const props = withDefaults(defineProps<{
  // 图表配置数组
  charts: ChartConfig[];
  // 图表高度
  height?: string;
  // 图表背景色
  backgroundColor?: string;
  // 是否显示标题
  showTitle?: boolean;
  // 标题图片路径，如果提供则使用图片代替文字标题
  titleImage?: string;
}>(), {
  height: '133px', // 使用与父组件中相同的高度
  backgroundColor: 'rgba(0, 42, 67, 0.3)',
  showTitle: true
});

// 图表实例引用
const chartInstances = ref<Record<string, echarts.ECharts>>({});

// 初始化图表
const initChart = (id: string, config: ChartConfig) => {
  const chartDom = document.getElementById(id);
  if (!chartDom) return;

  // 创建图表实例
  const chart = echarts.init(chartDom);
  chartInstances.value[id] = chart;

  // 设置图表选项
  const option = {
    backgroundColor: 'rgba(255, 255, 255, 0.9)', // 浅色背景
    title: props.showTitle ? {
      text: config.title,
      textStyle: {
        color: '#409eff', // 蓝色标题
        fontSize: 12, // 减小字体大小
        fontWeight: 'normal'
      },
      left: 10,
      top: 2 // 减小顶部距离
    } : undefined,
    grid: {
      top: props.showTitle ? 15 : 5, // 进一步减小顶部空间
      left: 50,
      right: 10,
      bottom: 20, // 减小底部空间，但仍确保X轴可见
      containLabel: true // 确保标签包含在网格内
    },
    xAxis: {
      type: 'category',
      data: config.data.map(item => item.date),
      axisLine: {
        lineStyle: {
          color: '#e4e7ed'
        }
      },
      axisTick: {
        show: false
      },
      axisLabel: {
        color: '#666666',
        fontSize: 10, // 减小字体大小
        interval: 2, // 显示间隔，使标签不那么拥挤
        margin: 5, // 进一步减小标签与轴线的距离
        align: 'center', // 水平居中对齐
        hideOverlap: true // 隐藏重叠标签
      },
      splitLine: {
        show: true,
        lineStyle: {
          color: '#e4e7ed',
          type: 'dashed'
        }
      }
    },
    yAxis: {
      type: 'value',
      min: 0,
      max: 400,
      interval: 100, // 增大间隔，减少刻度数量
      axisLine: {
        show: false
      },
      axisTick: {
        show: false
      },
      axisLabel: {
        color: '#666666',
        fontSize: 10, // 减小字体大小
        margin: 3 // 进一步减小标签与轴线的距离
      },
      splitLine: {
        show: true,
        lineStyle: {
          color: '#e4e7ed',
          type: 'dashed'
        }
      }
    },
    tooltip: {
      trigger: 'axis',
      backgroundColor: 'rgba(255, 255, 255, 0.95)',
      borderColor: '#e4e7ed',
      textStyle: {
        color: '#333333'
      },
      formatter: (params: any) => {
        const param = params[0];
        return `${param.name}<br/>值: ${param.value}`;
      }
    },
    series: [
      {
        data: config.data.map(item => item.value),
        type: 'line',
        smooth: true,
        symbol: 'none',
        lineStyle: {
          width: 1, // 减小线条宽度，适应较小的图表高度
          color: config.color,
          shadowColor: config.color,
          shadowBlur: 3 // 减小阴影效果
        },
        areaStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              {
                offset: 0,
                color: config.color // 渐变起始色
              },
              {
                offset: 0.3, // 调整中间点位置，使渐变在小高度下更明显
                color: `${config.color}60` // 半透明的中间色
              },
              {
                offset: 1,
                color: 'rgba(255, 255, 255, 0.05)' // 更透明的结束色
              }
            ]
          },
          opacity: 0.7 // 调整透明度
        }
      }
    ]
  };

  // 设置图表选项
  chart.setOption(option);
};

// 监听窗口大小变化，调整图表大小
const handleResize = () => {
  Object.values(chartInstances.value).forEach(chart => {
    chart.resize();
  });
};

// 监听数据变化，更新图表
watch(() => props.charts, (newCharts) => {
  // 延迟执行，确保DOM已经更新
  setTimeout(() => {
    newCharts.forEach(config => {
      if (chartInstances.value[config.id]) {
        // 更新已存在的图表
        const chart = chartInstances.value[config.id];
        chart.setOption({
          xAxis: {
            data: config.data.map(item => item.date)
          },
          series: [
            {
              data: config.data.map(item => item.value)
            }
          ]
        });
      } else {
        // 初始化新图表
        initChart(config.id, config);
      }
    });
  }, 0);
}, { deep: true });

// 组件挂载时初始化图表
onMounted(() => {
  // 初始化所有图表
  props.charts.forEach(config => {
    initChart(config.id, config);
  });

  // 添加窗口大小变化监听
  window.addEventListener('resize', handleResize);
});

// 组件卸载时清理图表实例
onBeforeUnmount(() => {
  // 销毁所有图表实例
  Object.values(chartInstances.value).forEach(chart => {
    chart.dispose();
  });

  // 移除窗口大小变化监听
  window.removeEventListener('resize', handleResize);
});
</script>

<template>
  <div class="resource-usage-charts">
    <div v-for="chart in props.charts" :key="chart.id" class="chart-wrapper">
      <!-- 标题图片 -->
      <img
        v-if="props.titleImage || chart.titleImage"
        :src="chart.titleImage || props.titleImage"
        class="chart-title-image"
        alt="图表标题"
      >
      <!-- 图表容器 -->
      <div
        :id="chart.id"
        class="chart-container"
        :style="{ height: props.height }"
      >
        <!-- 图表将在这里渲染 -->
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.resource-usage-charts {
  width: 100%;
  height: 100%; // 使用完整高度
  display: flex;
  flex-direction: column;
  gap: 2px; // 减少图表之间的间距
  padding: 0; // 移除内边距

  .chart-wrapper {
    display: flex;
    flex-direction: column;
    width: 100%;
    margin-bottom: 0; // 移除底部边距
  }

  .chart-title-image {
    height: 3.3rem; // 与其他组件中的图片高度一致
    width: 10rem;
    margin-bottom: 0; // 移除图片与图表之间的间距
  }

  .chart-container {
    width: 100%;
    height: 100%; // 确保图表容器占满父元素高度
    border-radius: 4px;
    overflow: visible; // 改为visible，允许内容溢出，确保X轴标签可见
    display: flex; // 使用flex布局
    flex-direction: column; // 垂直方向排列
    flex: 1; // 允许容器伸展填满可用空间
    margin-top: -5px; // 向上移动，减少与标题图片的间距
  }
}
</style>
