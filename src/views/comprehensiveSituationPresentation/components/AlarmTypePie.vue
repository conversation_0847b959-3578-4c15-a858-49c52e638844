<template>
  <div class="alarm-type-pie-container">
    <div class="alarm-title">
      <img :src="alarmTypeTitleImg" alt="告警类别-TOP5" class="title-image" />
    </div>
    <div ref="chartRef" class="alarm-type-pie"></div>
  </div>
</template>

<script lang="ts">
import { defineComponent } from 'vue';

export default defineComponent({
  name: 'AlarmTypePie'
});
</script>

<script setup lang="ts">
import { ref, onMounted, watch, onUnmounted } from 'vue';
import * as echarts from 'echarts/core';
import { PieChart } from 'echarts/charts';
import {
  TitleComponent,
  TooltipComponent,
  LegendComponent
} from 'echarts/components';
import { CanvasRenderer } from 'echarts/renderers';
import alarmTypeTitleImg from '../assets/CPU利用率趋势图-标题@3x(5).png';

// 注册必要的组件
echarts.use([
  PieChart,
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  CanvasRenderer
]);

// 定义图表数据类型
interface AlarmTypeItem {
  name: string;
  value: number;
  color: string;
}

const props = defineProps<{
  data: AlarmTypeItem[];
}>();

const chartRef = ref<HTMLElement | null>(null);
let chart: echarts.ECharts | null = null;

// 初始化图表
const initChart = () => {
  if (!chartRef.value) return;

  chart = echarts.init(chartRef.value);

  const option = {
    backgroundColor: 'transparent',
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} ({d}%)',
      backgroundColor: 'rgba(10, 20, 35, 0.8)',
      borderColor: '#315d8b',
      textStyle: {
        color: '#fff'
      }
    },
    legend: {
      orient: 'vertical',
      right: '0%',
      top: 'middle',
      itemWidth: 10,
      itemHeight: 10,
      icon: 'circle',
      itemGap: 16,
      textStyle: {
        color: '#fff',
        fontSize: 14
      }
    },
    series: [
      {
        name: '告警类型',
        type: 'pie',
        radius: ['25%', '70%'],
        center: ['32%', '50%'],
        startAngle: 200,
        avoidLabelOverlap: false,
        roseType: 'radius',
        itemStyle: {
          borderRadius: 0,
          borderColor: '#000c17',
          borderWidth: 2
        },
        label: {
          show: false
        },
        emphasis: {
          label: {
            show: false
          },
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        },
        labelLine: {
          show: false
        },
        data: props.data.map(item => ({
          value: item.value,
          name: item.name,
          itemStyle: {
            color: item.color
          }
        }))
      }
    ]
  };

  chart.setOption(option);
};

// 更新图表
const updateChart = () => {
  if (!chart) return;

  chart.setOption({
    series: [
      {
        data: props.data.map(item => ({
          value: item.value,
          name: item.name,
          itemStyle: {
            color: item.color
          }
        }))
      }
    ]
  });
};

// 监听窗口大小变化，调整图表大小
const handleResize = () => {
  chart && chart.resize();
};

// 监听数据变化，更新图表
watch(() => props.data, () => {
  if (chart) {
    updateChart();
  } else {
    initChart();
  }
}, { deep: true });

// 组件挂载时初始化图表
onMounted(() => {
  initChart();
  window.addEventListener('resize', handleResize);
});

// 组件卸载时清理图表实例
onUnmounted(() => {
  if (chart) {
    chart.dispose();
    chart = null;
  }
  window.removeEventListener('resize', handleResize);
});
</script>

<style lang="scss" scoped>
.alarm-type-pie-container {
  position: relative;
  width: 100%;
  height: 100%;
  padding: 10px;
  background-color: rgba(0, 24, 51, 0.5);
  border-radius: 4px;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;

  .alarm-title {
    margin-bottom: 5px;

    .title-image {
      position: absolute;
      top: -33px;
      left: -4px;
      height: 63px;
      object-fit: contain;
      margin-top: 5px;
      margin-bottom: 5px;
    }
  }

  .alarm-type-pie {
    width: 100%;
    flex: 1;
    min-height: 180px;
    margin-top: 10px;
  }

  :deep(.el-tooltip__trigger) {
    display: flex;
    align-items: center;
  }
}
</style>
