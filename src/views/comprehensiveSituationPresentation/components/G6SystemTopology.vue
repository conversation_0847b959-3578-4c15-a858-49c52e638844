<template>
  <div class="g6-system-topology-container" ref="containerRef">
    <div v-if="loading" class="loading-overlay">
      <div class="loading-spinner"></div>
    </div>

    <div v-if="error" class="error-message">
      {{ error }}
    </div>

    <div class="topology-background">
      <!-- 动态渲染图层背景 -->
      <div
        v-for="layer in internalLayers"
        :key="layer.id"
        class="topology-layer"
        :class="layer.id + '-layer'"
      >
        <div class="layer-title">{{ layer.name }}</div>
      </div>

      <!-- G6 图形容器 -->
      <div id="topology-container" ref="graphRef" class="topology-graph"></div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted, onUnmounted, ref, defineProps, defineEmits, watch, nextTick, computed } from 'vue';
import { useResponsive } from '../utils/responsive';
// @ts-ignore
import { useTopologyData, NodeType, EdgeType, LayerType } from './useTopologyData';
import G6 from '@antv/g6';

// 导入图标资源
import securityIcon from '@/views/comprehensiveSituationPresentation/assets/颜值密码-图标-蓝@3x.png';
import portalIcon from '@/views/comprehensiveSituationPresentation/assets/门户应用-图标-蓝@3x.png';
import todoIcon from '@/views/comprehensiveSituationPresentation/assets/待办／已办-图标-蓝@3x.png';
import processIcon from '@/views/comprehensiveSituationPresentation/assets/流程起草-图标-蓝@3x.png';
import tomcatIcon from '@/views/comprehensiveSituationPresentation/assets/tomcat-图标-蓝@3x.png';
import mysqlIcon from '@/views/comprehensiveSituationPresentation/assets/Mysql-图标-蓝@3x.png';
import redisIcon from '@/views/comprehensiveSituationPresentation/assets/Redis-图标-蓝@3x.png';
import serverIcon from '@/views/comprehensiveSituationPresentation/assets/服务器-图标@3x.png';
import cloudIcon from '@/views/comprehensiveSituationPresentation/assets/云平台-图标-蓝@3x.png';
// eslint-disable-next-line @typescript-eslint/no-unused-vars
import bgImage from '@/views/comprehensiveSituationPresentation/assets/系统构架-底层@3x.png';

// 定义组件属性
const props = defineProps({
  // 节点数据
  nodes: {
    type: Array as () => NodeType[],
    default: () => []
  },
  // 连线数据
  edges: {
    type: Array as () => EdgeType[],
    default: () => []
  },
  // 图层数据
  layers: {
    type: Array as () => LayerType[],
    default: () => []
  },
  // 是否自动加载数据
  autoLoad: {
    type: Boolean,
    default: false
  },
  // 数据加载URL
  dataUrl: {
    type: String,
    default: ''
  },
  // 是否启用动画
  enableAnimation: {
    type: Boolean,
    default: true
  },
  // SVG宽度
  svgWidth: {
    type: Number,
    default: 600
  },
  // SVG高度
  svgHeight: {
    type: Number,
    default: 400
  },
  // 初始缩放比例
  initialZoom: {
    type: Number,
    default: 0.8
  }
});

// 定义事件
const emit = defineEmits(['nodeClick', 'edgeClick', 'dataLoaded', 'error']);

// 内部状态
const loading = ref(false);
const error = ref('');
const internalNodes = ref<NodeType[]>([]);
const internalEdges = ref<EdgeType[]>([]);
const internalLayers = ref<LayerType[]>([]);
const containerRef = ref<HTMLElement | null>(null);
const graphRef = ref<HTMLElement | null>(null);
let graph: any = null;

// 响应式布局
// eslint-disable-next-line @typescript-eslint/no-unused-vars
const { screenWidth, isMobile, isTablet } = useResponsive();

// 计算节点大小
const nodeSize = computed(() => {
  return isMobile.value ? 30 : isTablet.value ? 35 : 40;
});

// 计算边的宽度
const edgeWidth = computed(() => {
  return isMobile.value ? 1 : 2;
});

// 使用自定义hook加载数据
const { loadTopologyData } = useTopologyData();

// 图标映射
const iconMap: Record<string, string> = {
  security: securityIcon,
  portal: portalIcon,
  todo: todoIcon,
  process: processIcon,
  tomcat1: tomcatIcon,
  tomcat2: tomcatIcon,
  mysql: mysqlIcon,
  redis: redisIcon,
  server1: serverIcon,
  server2: serverIcon,
  server3: serverIcon,
  cloud: cloudIcon
};

// 注册自定义节点
const registerCustomNode = () => {
  // 注册自定义节点
  G6.registerNode('custom-node', {
    draw(cfg: any, group: any) {
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
      const { id, name, type, icon, layer, status } = cfg;

      // 创建节点容器
      const container = group.addGroup({
        id: `node-container-${id}`
      });

      // 添加图标
      const iconSize = type === 'server' ? 40 : 40;
      const iconObj = container.addShape('image', {
        attrs: {
          x: -iconSize / 2,
          y: -iconSize / 2,
          width: iconSize,
          height: iconSize,
          img: icon || iconMap[id] || '',
          cursor: 'pointer'
        },
        name: 'node-icon',
        draggable: true
      });

      // 添加文本标签
      container.addShape('text', {
        attrs: {
          text: name,
          x: 0,
          y: iconSize / 2 + 10,
          textAlign: 'center',
          textBaseline: 'middle',
          fill: '#fff',
          fontSize: 10,
          fontWeight: 'normal',
          cursor: 'pointer'
        },
        name: 'node-label',
        draggable: true
      });

      // 根据状态添加效果
      if (status && status !== 'normal') {
        let shadowColor = '#00eaff';
        if (status === 'warning') shadowColor = '#ffcc00';
        if (status === 'error') shadowColor = '#ff4d4f';
        if (status === 'offline') {
          iconObj.attr('opacity', 0.5);
        } else {
          container.addShape('circle', {
            attrs: {
              x: 0,
              y: 0,
              r: iconSize / 2 + 5,
              fill: 'transparent',
              stroke: shadowColor,
              lineWidth: 2,
              opacity: 0.7,
              shadowColor,
              shadowBlur: 10
            },
            name: 'node-status'
          });
        }
      }

      return container;
    },
    update(cfg: any, node: any) {
      const container = node.getContainer();
      const icon = container.find((element: any) => element.get('name') === 'node-icon');
      const label = container.find((element: any) => element.get('name') === 'node-label');
      const status = container.find((element: any) => element.get('name') === 'node-status');

      if (icon) {
        icon.attr('img', cfg.icon || iconMap[cfg.id] || '');
      }

      if (label) {
        label.attr('text', cfg.name);
      }

      if (cfg.status && cfg.status !== 'normal') {
        if (!status) {
          // 如果状态变化，添加状态效果
          let shadowColor = '#00eaff';
          if (cfg.status === 'warning') shadowColor = '#ffcc00';
          if (cfg.status === 'error') shadowColor = '#ff4d4f';

          if (cfg.status === 'offline') {
            icon.attr('opacity', 0.5);
          } else {
            const iconSize = cfg.type === 'server' ? 40 : 40;
            container.addShape('circle', {
              attrs: {
                x: 0,
                y: 0,
                r: iconSize / 2 + 3,
                fill: 'transparent',
                stroke: shadowColor,
                lineWidth: 1.5,
                opacity: 0.7,
                shadowColor,
                shadowBlur: 8
              },
              name: 'node-status'
            });
          }
        } else {
          // 更新状态效果
          let shadowColor = '#00eaff';
          if (cfg.status === 'warning') shadowColor = '#ffcc00';
          if (cfg.status === 'error') shadowColor = '#ff4d4f';

          if (cfg.status === 'offline') {
            icon.attr('opacity', 0.5);
            status.remove();
          } else {
            status.attr({
              stroke: shadowColor,
              shadowColor
            });
          }
        }
      } else if (status) {
        // 如果状态恢复正常，移除状态效果
        status.remove();
        icon.attr('opacity', 1);
      }
    }
  });

  // 注册自定义边
  G6.registerEdge('custom-edge', {
    draw(cfg: any, group: any) {
      const { startPoint, endPoint, status } = cfg;

      // 确定边的颜色
      let strokeColor = '#00eaff';
      let arrowFill = '#00eaff';

      if (status === 'warning') {
        strokeColor = '#ffcc00';
        arrowFill = '#ffcc00';
      } else if (status === 'error') {
        strokeColor = '#ff4d4f';
        arrowFill = '#ff4d4f';
      }

      // 创建主路径
      const path = group.addShape('path', {
        attrs: {
          path: [
            ['M', startPoint.x, startPoint.y],
            ['C',
              startPoint.x, startPoint.y + (endPoint.y - startPoint.y) / 3,
              endPoint.x, endPoint.y - (endPoint.y - startPoint.y) / 3,
              endPoint.x, endPoint.y
            ]
          ],
          stroke: strokeColor,
          lineWidth: 2,
          opacity: 0.8,
          cursor: 'pointer',
          // 添加虚线效果
          lineDash: [4, 4]
        },
        name: 'edge-path'
      });

      // 创建箭头
      const arrowSize = 12; // 箭头大小
      const arrowOffset = 5; // 箭头偏移量

      // 计算箭头位置和方向
      const dx = endPoint.x - startPoint.x;
      const dy = endPoint.y - startPoint.y;
      const angle = Math.atan2(dy, dx);

      // 计算箭头顶点位置
      const arrowX = endPoint.x - arrowOffset * Math.cos(angle);
      const arrowY = endPoint.y - arrowOffset * Math.sin(angle);

      // 创建箭头
      group.addShape('path', {
        attrs: {
          path: [
            ['M', arrowX - arrowSize * Math.cos(angle - Math.PI / 6), arrowY - arrowSize * Math.sin(angle - Math.PI / 6)],
            ['L', arrowX, arrowY],
            ['L', arrowX - arrowSize * Math.cos(angle + Math.PI / 6), arrowY - arrowSize * Math.sin(angle + Math.PI / 6)],
            ['Z']
          ],
          fill: arrowFill,
          stroke: arrowFill,
          lineWidth: 1,
          opacity: 1
        },
        name: 'edge-arrow'
      });

      // 添加流动效果
      if (props.enableAnimation) {
        // 创建流动线
        const flowPath = group.addShape('path', {
          attrs: {
            path: [
              ['M', startPoint.x, startPoint.y],
              ['C',
                startPoint.x, startPoint.y + (endPoint.y - startPoint.y) / 3,
                endPoint.x, endPoint.y - (endPoint.y - startPoint.y) / 3,
                endPoint.x, endPoint.y
              ]
            ],
            stroke: strokeColor,
            lineWidth: 1,
            opacity: 0.5,
            lineDash: [4, 8],
            lineDashOffset: 0
          },
          name: 'edge-flow'
        });

        // 添加动画
        flowPath.animate(
          {
            lineDashOffset: -100
          },
          {
            duration: 3000,
            repeat: true,
            easing: 'linear'
          }
        );
      }

      return path;
    },
    update(cfg: any, edge: any) {
      const { startPoint, endPoint, status } = cfg;
      const group = edge.getContainer();
      const path = edge.get('keyShape');
      const arrow = group.find((element: any) => element.get('name') === 'edge-arrow');
      const flowPath = group.find((element: any) => element.get('name') === 'edge-flow');

      // 确定边的颜色
      let strokeColor = '#00eaff';
      let arrowFill = '#00eaff';

      if (status === 'warning') {
        strokeColor = '#ffcc00';
        arrowFill = '#ffcc00';
      } else if (status === 'error') {
        strokeColor = '#ff4d4f';
        arrowFill = '#ff4d4f';
      }

      // 更新路径
      path.attr({
        path: [
          ['M', startPoint.x, startPoint.y],
          ['C',
            startPoint.x, startPoint.y + (endPoint.y - startPoint.y) / 3,
            endPoint.x, endPoint.y - (endPoint.y - startPoint.y) / 3,
            endPoint.x, endPoint.y
          ]
        ],
        stroke: strokeColor
      });

      // 计算箭头位置和方向
      const dx = endPoint.x - startPoint.x;
      const dy = endPoint.y - startPoint.y;
      const angle = Math.atan2(dy, dx);

      const arrowSize = 12; // 箭头大小
      const arrowOffset = 5; // 箭头偏移量

      // 计算箭头顶点位置
      const arrowX = endPoint.x - arrowOffset * Math.cos(angle);
      const arrowY = endPoint.y - arrowOffset * Math.sin(angle);

      // 更新箭头
      if (arrow) {
        arrow.attr({
          path: [
            ['M', arrowX - arrowSize * Math.cos(angle - Math.PI / 6), arrowY - arrowSize * Math.sin(angle - Math.PI / 6)],
            ['L', arrowX, arrowY],
            ['L', arrowX - arrowSize * Math.cos(angle + Math.PI / 6), arrowY - arrowSize * Math.sin(angle + Math.PI / 6)],
            ['Z']
          ],
          fill: arrowFill,
          stroke: arrowFill
        });
      }

      // 更新流动线
      if (flowPath) {
        flowPath.attr({
          path: [
            ['M', startPoint.x, startPoint.y],
            ['C',
              startPoint.x, startPoint.y + (endPoint.y - startPoint.y) / 3,
              endPoint.x, endPoint.y - (endPoint.y - startPoint.y) / 3,
              endPoint.x, endPoint.y
            ]
          ],
          stroke: strokeColor
        });
      }
    }
  });
};

// 初始化图形
const initGraph = () => {
  if (!graphRef.value) return;

  // 注册自定义节点和边
  registerCustomNode();

  // 获取容器尺寸
  const container = graphRef.value;
  const width = container.clientWidth || 600;
  const height = container.clientHeight || 400;

  // 创建图实例
  graph = new G6.Graph({
    container: container,
    width,
    height,
    modes: {
      default: ['drag-canvas'] // 移除 'zoom-canvas' 以禁用鼠标滚轮缩放
    },
    // 初始缩放比例将在图渲染后设置
    defaultNode: {
      type: 'custom-node',
      size: nodeSize.value
    },
    defaultEdge: {
      type: 'custom-edge',
      style: {
        stroke: '#00eaff',
        lineWidth: edgeWidth.value,
        opacity: 0.8,
        cursor: 'pointer'
      }
    },
    nodeStateStyles: {
      hover: {
        opacity: 0.8,
        shadowColor: '#00eaff',
        shadowBlur: 10
      }
    },
    edgeStateStyles: {
      hover: {
        stroke: '#fff',
        lineWidth: 3
      }
    },
    // 禁用自动布局，我们将手动设置节点位置
    layout: null,
    fitView: false,
    animate: true
  });

  // 绑定事件
  bindEvents();

  // 渲染图形
  renderGraph();
};

// 绑定事件
const bindEvents = () => {
  if (!graph) return;

  // 节点点击事件
  graph.on('node:click', (evt: any) => {
    const { item } = evt;
    const model = item.getModel();
    const node = internalNodes.value.find(n => n.id === model.id);
    if (node) {
      emit('nodeClick', node);
    }
  });

  // 边点击事件
  graph.on('edge:click', (evt: any) => {
    const { item } = evt;
    const model = item.getModel();
    const edge = internalEdges.value.find(e => e.id === model.id);
    if (edge) {
      emit('edgeClick', edge);
    }
  });

  // 节点悬停效果
  graph.on('node:mouseenter', (evt: any) => {
    const { item } = evt;
    graph.setItemState(item, 'hover', true);
  });

  graph.on('node:mouseleave', (evt: any) => {
    const { item } = evt;
    graph.setItemState(item, 'hover', false);
  });

  // 边悬停效果
  graph.on('edge:mouseenter', (evt: any) => {
    const { item } = evt;
    graph.setItemState(item, 'hover', true);
  });

  graph.on('edge:mouseleave', (evt: any) => {
    const { item } = evt;
    graph.setItemState(item, 'hover', false);
  });

  // 窗口大小变化事件
  window.addEventListener('resize', handleResize);
};

// 处理窗口大小变化
const handleResize = () => {
  if (!graph || !graphRef.value) return;

  const container = graphRef.value;
  const width = container.clientWidth || 600;
  const height = container.clientHeight || 400;

  graph.changeSize(width, height);

  // 更新节点大小
  graph.updateItem('node', {
    size: nodeSize.value
  });

  // 更新边的宽度
  graph.updateItem('edge', {
    style: {
      lineWidth: edgeWidth.value
    }
  });

  // 重新渲染图形，使节点位置适应新的尺寸
  renderGraph();
};

// 渲染图形
const renderGraph = () => {
  if (!graph) return;

  // 获取容器尺寸
  const width = graph.getWidth();
  const height = graph.getHeight();

  // 计算每一层的高度
  const layerHeight = height / internalLayers.value.length;

  // 准备数据
  const nodes = internalNodes.value.map(node => {
    // 根据图层计算节点的初始位置
    const layer = internalLayers.value.find(l => l.id === node.layer);
    const layerIndex = layer ? layer.order - 1 : 0;
    const layerNodes = internalNodes.value.filter(n => n.layer === node.layer);
    const nodeIndex = layerNodes.findIndex(n => n.id === node.id);
    const totalNodes = layerNodes.length;

    // 计算节点在图层中的相对位置
    // 水平均匀分布
    const horizontalPadding = width * 0.1; // 10% 的内边距
    const availableWidth = width - 2 * horizontalPadding;
    const x = horizontalPadding + (nodeIndex * availableWidth / (totalNodes - 1 || 1));

    // 垂直位置调整，向上移动一些
    const y = layerHeight * (layerIndex + 0.4);

    return {
      ...node,
      x,
      y,
      type: 'custom-node',
      size: 40, // 将在渲染时动态调整
      style: {
        fill: '#1E1E1E',
        stroke: '#00eaff',
        lineWidth: 1
      }
    };
  });

  const edges = internalEdges.value.map(edge => {
    return {
      ...edge,
      type: 'custom-edge'
    };
  });

  // 更新数据
  graph.data({
    nodes,
    edges
  });

  // 渲染图形
  graph.render();

  // 设置初始缩放比例
  graph.zoomTo(props.initialZoom); // 使用传入的初始缩放比例

  // 将图形居中显示
  graph.fitCenter();
};

// 加载数据方法
const loadData = async () => {
  if (!props.dataUrl && (!props.nodes.length || !props.edges.length)) {
    // 如果没有提供数据URL且没有提供节点和连线数据，则使用默认数据
    useDefaultData();
    return;
  }

  if (props.dataUrl) {
    loading.value = true;
    error.value = '';

    try {
      const data = await loadTopologyData(props.dataUrl);
      internalNodes.value = data.nodes || [];
      internalEdges.value = data.edges || [];
      internalLayers.value = data.layers || [];
      emit('dataLoaded', { nodes: internalNodes.value, edges: internalEdges.value, layers: internalLayers.value });
    } catch (err) {
      error.value = err instanceof Error ? err.message : '加载拓扑数据失败';
      emit('error', error.value);
      // 加载失败时使用默认数据
      useDefaultData();
    } finally {
      loading.value = false;
    }
  } else {
    // 使用传入的数据
    internalNodes.value = props.nodes;
    internalEdges.value = props.edges;
    internalLayers.value = props.layers;
  }

  // 如果图形已初始化，更新图形
  if (graph) {
    renderGraph();
  }
};

// 使用默认数据
const useDefaultData = () => {
  // 默认图层
  internalLayers.value = [
    { id: 'application', name: '应用服务层', order: 1 },
    { id: 'middleware', name: '中间件层', order: 2 },
    { id: 'infrastructure', name: '基础设施层', order: 3 }
  ];

  // 默认节点
  internalNodes.value = [
    // 应用服务层节点
    { id: 'security', name: '安全验证', type: 'circle', icon: securityIcon, layer: 'application' },
    { id: 'portal', name: '门户应用', type: 'circle', icon: portalIcon, layer: 'application' },
    { id: 'todo', name: '待办/已办数据加载', type: 'circle', icon: todoIcon, layer: 'application' },
    { id: 'process', name: '流程起草', type: 'circle', icon: processIcon, layer: 'application' },

    // 中间件层节点
    { id: 'tomcat1', name: 'Tomcat1', type: 'circle', icon: tomcatIcon, layer: 'middleware' },
    { id: 'tomcat2', name: 'Tomcat2', type: 'circle', icon: tomcatIcon, layer: 'middleware' },
    { id: 'mysql', name: 'Mysql', type: 'circle', icon: mysqlIcon, layer: 'middleware' },
    { id: 'redis', name: 'Redis', type: 'circle', icon: redisIcon, layer: 'middleware' },

    // 基础设施层节点
    { id: 'server1', name: '服务器1', type: 'server', icon: serverIcon, layer: 'infrastructure' },
    { id: 'server2', name: '服务器2', type: 'server', icon: serverIcon, layer: 'infrastructure' },
    { id: 'cloud', name: '云平台', type: 'server', icon: cloudIcon, layer: 'infrastructure' },
    { id: 'server3', name: '服务器3', type: 'server', icon: serverIcon, layer: 'infrastructure' }
  ];

  // 默认连线
  internalEdges.value = [
    // 应用层到中间件层
    { id: 'e1', source: 'security', target: 'tomcat1' },
    { id: 'e2', source: 'security', target: 'tomcat2' },
    { id: 'e3', source: 'portal', target: 'tomcat1' },
    { id: 'e4', source: 'portal', target: 'tomcat2' },
    { id: 'e5', source: 'todo', target: 'mysql' },
    { id: 'e6', source: 'process', target: 'redis' },

    // 中间件层到基础设施层
    { id: 'e7', source: 'tomcat1', target: 'server1' },
    { id: 'e8', source: 'tomcat1', target: 'server2' },
    { id: 'e9', source: 'tomcat2', target: 'server2' },
    { id: 'e10', source: 'mysql', target: 'cloud' },
    { id: 'e11', source: 'redis', target: 'server3' }
  ];

  // 如果图形已初始化，更新图形
  if (graph) {
    renderGraph();
  }
};

// 监听属性变化
watch(() => props.nodes, (newNodes) => {
  if (newNodes.length > 0) {
    internalNodes.value = newNodes;
    if (graph) {
      renderGraph();
    }
  }
}, { deep: true });

watch(() => props.edges, (newEdges) => {
  if (newEdges.length > 0) {
    internalEdges.value = newEdges;
    if (graph) {
      renderGraph();
    }
  }
}, { deep: true });

watch(() => props.layers, (newLayers) => {
  if (newLayers.length > 0) {
    internalLayers.value = newLayers;
    if (graph) {
      renderGraph();
    }
  }
}, { deep: true });

// 生命周期钩子
onMounted(() => {
  if (props.autoLoad) {
    loadData();
  } else if (!props.nodes.length && !props.edges.length) {
    // 如果没有提供数据，使用默认数据
    useDefaultData();
  }

  // 初始化图形
  nextTick(() => {
    initGraph();
  });
});

onUnmounted(() => {
  // 移除事件监听器
  window.removeEventListener('resize', handleResize);

  // 销毁图形
  if (graph) {
    graph.destroy();
    graph = null;
  }
});

// 暴露方法给父组件
defineExpose({
  loadData,
  nodes: internalNodes,
  edges: internalEdges,
  layers: internalLayers
});
</script>

<style scoped>
.g6-system-topology-container {
  width: 100%;
  height: 100%;
  position: relative;
  background-color: transparent;
  overflow: hidden;

  /* 响应式调整 */
  @media screen and (max-width: 768px) {
    min-height: 200px;
  }
}

.topology-background {
  width: 100%;
  height: 100%;
  position: relative;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.topology-layer {
  position: relative;
  height: 23.33%;
  width: 100%;
  background-image: url('../assets/系统构架-底层@3x.png');
  background-size: 100% 100%;
  background-repeat: no-repeat;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  z-index: 1;
}

.layer-title {
  position: absolute;
  bottom: 4px;
  color: #fff;
  font-size: 16px;
  font-weight: bold;
  text-align: center;
  width: 100%;
}

.topology-graph {
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
  background-color: transparent;
  z-index: 2;
}

/* 加载中样式 */
.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 10;
}

.loading-spinner {
  width: 50px;
  height: 50px;
  border: 3px solid rgba(0, 234, 255, 0.3);
  border-radius: 50%;
  border-top-color: #00eaff;
  animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

/* 错误消息样式 */
.error-message {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background-color: rgba(255, 77, 79, 0.8);
  color: white;
  padding: 10px 20px;
  border-radius: 4px;
  z-index: 5;
  max-width: 80%;
  text-align: center;
}

/* 添加数据流动效果 */
@keyframes dataFlow {
  0% {
    stroke-dashoffset: 0;
    stroke-dasharray: 0, 20;
    opacity: 0;
  }
  20% {
    opacity: 0.8;
  }
  100% {
    stroke-dashoffset: -1000;
    stroke-dasharray: 5, 15;
    opacity: 0;
  }
}

/* 添加节点浮动效果 */
@keyframes float {
  0% {
    transform: translateY(0) scale(1);
    filter: drop-shadow(0 0 3px rgba(0, 234, 255, 0.2));
  }
  50% {
    transform: translateY(-5px) scale(1.03);
    filter: drop-shadow(0 5px 8px rgba(0, 234, 255, 0.4));
  }
  100% {
    transform: translateY(0) scale(1);
    filter: drop-shadow(0 0 3px rgba(0, 234, 255, 0.2));
  }
}
</style>
