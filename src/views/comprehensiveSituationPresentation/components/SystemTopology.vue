<template>
  <div class="system-topology-container" ref="containerRef">
    <div v-if="loading" class="loading-overlay">
      <div class="loading-spinner"></div>
    </div>

    <div v-if="error" class="error-message">
      {{ error }}
    </div>

    <div class="topology-background">
      <!-- 动态渲染图层 -->
      <div
        v-for="layer in internalLayers"
        :key="layer.id"
        class="topology-layer"
        :class="layer.id + '-layer'"
      >
        <div class="layer-title">{{ layer.name }}</div>
        <div class="layer-nodes">
          <!-- 根据图层筛选并渲染节点 -->
          <div
            v-for="node in internalNodes.filter(n => n.layer === layer.id)"
            :key="node.id"
            class="node-item"
            :data-node-id="node.id"
          >
            <!-- 根据节点类型渲染不同样式 -->
            <div
              :class="node.type === 'server' ? 'node-server' : 'node-circle'"
              :data-status="node.status || 'normal'"
              @click="handleNodeClick(node)"
            >
              <img :src="node.icon" :alt="node.name" />
            </div>
            <div class="node-label">{{ node.name }}</div>
          </div>
        </div>
      </div>

      <!-- 连线 -->
      <svg ref="svgRef" class="topology-connections" :viewBox="`0 0 ${actualSvgWidth} ${actualSvgHeight}`" :width="actualSvgWidth" :height="actualSvgHeight" preserveAspectRatio="none">
        <!-- 添加箭头定义 -->
        <defs>
          <marker id="arrowhead" markerWidth="10" markerHeight="8" refX="10" refY="4" orient="auto">
            <polygon points="0 0, 10 4, 0 8" fill="#00eaff" />
          </marker>

          <!-- 添加不同状态的箭头 -->
          <marker id="arrowhead-warning" markerWidth="10" markerHeight="8" refX="10" refY="4" orient="auto">
            <polygon points="0 0, 10 4, 0 8" fill="#ffcc00" />
          </marker>

          <marker id="arrowhead-error" markerWidth="10" markerHeight="8" refX="10" refY="4" orient="auto">
            <polygon points="0 0, 10 4, 0 8" fill="#ff4d4f" />
          </marker>

          <!-- 添加流动效果的渐变 -->
          <linearGradient id="flow-gradient" x1="0%" y1="0%" x2="100%" y2="0%">
            <stop offset="0%" stop-color="rgba(0, 234, 255, 0.1)" />
            <stop offset="50%" stop-color="rgba(0, 234, 255, 0.6)" />
            <stop offset="100%" stop-color="rgba(0, 234, 255, 0.1)" />
            <animate attributeName="x1" from="0%" to="100%" dur="3s" repeatCount="indefinite" />
            <animate attributeName="x2" from="100%" to="200%" dur="3s" repeatCount="indefinite" />
          </linearGradient>
        </defs>

        <!-- 连线路径 -->
        <g>
          <path
            v-for="edge in internalEdges"
            :key="edge.id"
            :d="getEdgePath(edge)"
            class="connection-line"
            :class="edge.status ? 'status-' + edge.status : ''"
            @click="handleEdgeClick(edge)"
          />
        </g>

        <!-- 带流动效果的连线 -->
        <g stroke="url(#flow-gradient)" stroke-width="1.5" fill="none">
          <path
            v-for="edge in internalEdges"
            :key="'flow-' + edge.id"
            :d="getEdgePath(edge)"
            class="flow-line"
            :class="edge.status ? 'status-' + edge.status : ''"
          />
        </g>

        <!-- 带箭头的连线 -->
        <g stroke="#00eaff" stroke-width="2" fill="none" marker-end="url(#arrowhead)">
          <path
            v-for="edge in internalEdges"
            :key="'arrow-' + edge.id"
            :d="getEdgePath(edge)"
            :class="edge.status ? 'status-' + edge.status : ''"
            :marker-end="getEdgeMarker(edge)"
          />
        </g>
      </svg>
    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted, onUnmounted, ref, defineProps, defineEmits, watch, computed, nextTick } from 'vue';
import { useTopologyData } from './useTopologyData';

// 导入图标资源
import securityIcon from '@/views/comprehensiveSituationPresentation/assets/颜值密码-图标-蓝@3x.png';
import portalIcon from '@/views/comprehensiveSituationPresentation/assets/门户应用-图标-蓝@3x.png';
import todoIcon from '@/views/comprehensiveSituationPresentation/assets/待办／已办-图标-蓝@3x.png';
import processIcon from '@/views/comprehensiveSituationPresentation/assets/流程起草-图标-蓝@3x.png';
import tomcatIcon from '@/views/comprehensiveSituationPresentation/assets/tomcat-图标-蓝@3x.png';
import mysqlIcon from '@/views/comprehensiveSituationPresentation/assets/Mysql-图标-蓝@3x.png';
import redisIcon from '@/views/comprehensiveSituationPresentation/assets/Redis-图标-蓝@3x.png';
import serverIcon from '@/views/comprehensiveSituationPresentation/assets/服务器-图标@3x.png';
import cloudIcon from '@/views/comprehensiveSituationPresentation/assets/云平台-图标-蓝@3x.png';
import bgImage from '@/views/comprehensiveSituationPresentation/assets/系统构架-底层@3x.png';

// 定义节点类型
interface NodeType {
  id: string;
  name: string;
  type: string; // 'circle' | 'server' | 自定义类型
  icon: string;
  layer: string;
  position?: { x: number; y: number };
  status?: string; // 'normal' | 'warning' | 'error' | 'offline'
  data?: Record<string, any>; // 额外数据
}

// 定义连线类型
interface EdgeType {
  id: string;
  source: string; // 源节点ID
  target: string; // 目标节点ID
  type?: string; // 连线类型
  status?: string; // 'normal' | 'warning' | 'error'
  data?: Record<string, any>; // 额外数据
}

// 定义图层类型
interface LayerType {
  id: string;
  name: string;
  order: number; // 层级顺序，从上到下
}

// 定义组件属性
const props = defineProps({
  // 节点数据
  nodes: {
    type: Array as () => NodeType[],
    default: () => []
  },
  // 连线数据
  edges: {
    type: Array as () => EdgeType[],
    default: () => []
  },
  // 图层数据
  layers: {
    type: Array as () => LayerType[],
    default: () => []
  },
  // 是否自动加载数据
  autoLoad: {
    type: Boolean,
    default: false
  },
  // 数据加载URL
  dataUrl: {
    type: String,
    default: ''
  },
  // 是否启用动画
  enableAnimation: {
    type: Boolean,
    default: true
  },
  // SVG宽度
  svgWidth: {
    type: Number,
    default: 600
  },
  // SVG高度
  svgHeight: {
    type: Number,
    default: 400
  }
});

// 定义事件
const emit = defineEmits(['nodeClick', 'edgeClick', 'dataLoaded', 'error']);

// 内部状态
const loading = ref(false);
const error = ref('');
const internalNodes = ref<NodeType[]>([]);
const internalEdges = ref<EdgeType[]>([]);
const internalLayers = ref<LayerType[]>([]);
const containerRef = ref<HTMLElement | null>(null);
const svgRef = ref<SVGElement | null>(null);

// 计算实际的 SVG 尺寸
const actualSvgWidth = ref(props.svgWidth);
const actualSvgHeight = ref(props.svgHeight);

// 使用自定义hook加载数据
const { loadTopologyData } = useTopologyData();

// 更新 SVG 尺寸
const updateSvgSize = () => {
  if (containerRef.value) {
    const containerWidth = containerRef.value.clientWidth;
    const containerHeight = containerRef.value.clientHeight;

    // 如果容器尺寸有效，则使用容器尺寸，否则使用属性中的默认值
    actualSvgWidth.value = containerWidth > 0 ? containerWidth : props.svgWidth;
    actualSvgHeight.value = containerHeight > 0 ? containerHeight : props.svgHeight;

    // 强制重新渲染
    nextTick(() => {
      if (svgRef.value) {
        // 更新 viewBox
        svgRef.value.setAttribute('viewBox', `0 0 ${actualSvgWidth.value} ${actualSvgHeight.value}`);
      }
    });
  }
};

// 监听窗口大小变化
const addResizeListener = () => {
  window.addEventListener('resize', updateSvgSize);
};

const removeResizeListener = () => {
  window.removeEventListener('resize', updateSvgSize);
};

// 加载数据方法
const loadData = async () => {
  if (!props.dataUrl && (!props.nodes.length || !props.edges.length)) {
    // 如果没有提供数据URL且没有提供节点和连线数据，则使用默认数据
    useDefaultData();
    return;
  }

  if (props.dataUrl) {
    loading.value = true;
    error.value = '';

    try {
      const data = await loadTopologyData(props.dataUrl);
      internalNodes.value = data.nodes || [];
      internalEdges.value = data.edges || [];
      internalLayers.value = data.layers || [];
      emit('dataLoaded', { nodes: internalNodes.value, edges: internalEdges.value, layers: internalLayers.value });
    } catch (err) {
      error.value = err instanceof Error ? err.message : '加载拓扑数据失败';
      emit('error', error.value);
      // 加载失败时使用默认数据
      useDefaultData();
    } finally {
      loading.value = false;
    }
  } else {
    // 使用传入的数据
    internalNodes.value = props.nodes;
    internalEdges.value = props.edges;
    internalLayers.value = props.layers;
  }
};

// 使用默认数据
const useDefaultData = () => {
  // 默认图层
  internalLayers.value = [
    { id: 'application', name: '应用服务层', order: 1 },
    { id: 'middleware', name: '中间件层', order: 2 },
    { id: 'infrastructure', name: '基础设施层', order: 3 }
  ];

  // 默认节点
  internalNodes.value = [
    // 应用服务层节点
    { id: 'security', name: '安全验证', type: 'circle', icon: securityIcon, layer: 'application' },
    { id: 'portal', name: '门户应用', type: 'circle', icon: portalIcon, layer: 'application' },
    { id: 'todo', name: '待办/已办数据加载', type: 'circle', icon: todoIcon, layer: 'application' },
    { id: 'process', name: '流程起草', type: 'circle', icon: processIcon, layer: 'application' },

    // 中间件层节点
    { id: 'tomcat1', name: 'Tomcat1', type: 'circle', icon: tomcatIcon, layer: 'middleware' },
    { id: 'tomcat2', name: 'Tomcat2', type: 'circle', icon: tomcatIcon, layer: 'middleware' },
    { id: 'mysql', name: 'Mysql', type: 'circle', icon: mysqlIcon, layer: 'middleware' },
    { id: 'redis', name: 'Redis', type: 'circle', icon: redisIcon, layer: 'middleware' },

    // 基础设施层节点
    { id: 'server1', name: '服务器1', type: 'server', icon: serverIcon, layer: 'infrastructure' },
    { id: 'server2', name: '服务器2', type: 'server', icon: serverIcon, layer: 'infrastructure' },
    { id: 'cloud', name: '云平台', type: 'server', icon: cloudIcon, layer: 'infrastructure' },
    { id: 'server3', name: '服务器3', type: 'server', icon: serverIcon, layer: 'infrastructure' }
  ];

  // 默认连线
  internalEdges.value = [
    // 应用层到中间件层
    { id: 'e1', source: 'security', target: 'tomcat1' },
    { id: 'e2', source: 'security', target: 'tomcat2' },
    { id: 'e3', source: 'portal', target: 'tomcat1' },
    { id: 'e4', source: 'portal', target: 'tomcat2' },
    { id: 'e5', source: 'todo', target: 'mysql' },
    { id: 'e6', source: 'process', target: 'redis' },

    // 中间件层到基础设施层
    { id: 'e7', source: 'tomcat1', target: 'server1' },
    { id: 'e8', source: 'tomcat1', target: 'server2' },
    { id: 'e9', source: 'tomcat2', target: 'server2' },
    { id: 'e10', source: 'mysql', target: 'cloud' },
    { id: 'e11', source: 'redis', target: 'server3' }
  ];
};

// 处理节点点击事件
const handleNodeClick = (node: NodeType) => {
  emit('nodeClick', node);
};

// 添加节点动画效果
const addNodeAnimation = () => {
  if (!props.enableAnimation) return;

  const nodes = document.querySelectorAll('.node-circle');

  nodes.forEach((node, index) => {
    // 错开动画
    const delay = index * 0.5;
    (node as HTMLElement).style.animation = `float 3s ${delay}s ease-in-out infinite`;
  });

  // 添加连线动画
  const lines = document.querySelectorAll('.connection-line');
  lines.forEach((line) => {
    (line as SVGElement).style.strokeDasharray = '5, 5';
    (line as SVGElement).style.animation = 'dash 30s linear infinite';
  });
};

// 添加交互效果
const addInteractions = () => {
  const nodes = document.querySelectorAll('.node-circle, .node-server');

  nodes.forEach((node) => {
    const handleMouseEnter = () => {
      node.classList.add('hover');
    };

    const handleMouseLeave = () => {
      node.classList.remove('hover');
    };

    node.addEventListener('mouseenter', handleMouseEnter);
    node.addEventListener('mouseleave', handleMouseLeave);

    // 存储事件处理函数引用，以便于移除
    (node as any)._mouseEnterHandler = handleMouseEnter;
    (node as any)._mouseLeaveHandler = handleMouseLeave;
  });
};

// 获取连线路径
const getEdgePath = (edge: EdgeType): string => {
  // 这里可以根据节点位置计算连线路径
  // 简化版本，实际应用中可能需要更复杂的计算
  const sourceNode = internalNodes.value.find(node => node.id === edge.source);
  const targetNode = internalNodes.value.find(node => node.id === edge.target);

  if (!sourceNode || !targetNode) return '';

  // 简单示例，实际应用中需要根据节点在DOM中的实际位置计算
  // 这里假设节点已经有了位置信息
  const sourceLayer = internalLayers.value.find(layer => layer.id === sourceNode.layer);
  const targetLayer = internalLayers.value.find(layer => layer.id === targetNode.layer);

  if (!sourceLayer || !targetLayer) return '';

  // 根据层级顺序和节点在数组中的索引计算位置
  const sourceIndex = internalNodes.value.filter(n => n.layer === sourceNode.layer).findIndex(n => n.id === sourceNode.id);
  const targetIndex = internalNodes.value.filter(n => n.layer === targetNode.layer).findIndex(n => n.id === targetNode.id);

  // 计算每一层的基础高度
  const layerCount = internalLayers.value.length || 3; // 层数，默认为3
  const nodeCount = Math.max(
    ...internalLayers.value.map(layer =>
      internalNodes.value.filter(node => node.layer === layer.id).length
    )
  ) || 4; // 每层最大节点数，默认为4

  // 根据 SVG 尺寸计算各个参数
  const layerSpacing = actualSvgHeight.value / (layerCount + 1); // 每一层的高度
  const connectionOffset = 30; // 连线偏移量，调整连线与节点的距离

  // 计算节点的中心位置
  const horizontalPadding = actualSvgWidth.value * 0.1; // 水平方向的内边距
  const x1 = horizontalPadding + (sourceIndex + 0.5) * ((actualSvgWidth.value - 2 * horizontalPadding) / nodeCount);
  const y1Base = (sourceLayer.order) * layerSpacing;
  const x2 = horizontalPadding + (targetIndex + 0.5) * ((actualSvgWidth.value - 2 * horizontalPadding) / nodeCount);
  const y2Base = (targetLayer.order) * layerSpacing;

  // 计算连线的起点和终点，使其从图标的底部/顶部连接，而不是从文字处连接
  let y1: number, y2: number;

  if (sourceLayer.order < targetLayer.order) {
    // 如果源节点在上层，连线从图标底部出发
    y1 = y1Base + connectionOffset;
    // 目标节点在下层，连线到图标顶部
    y2 = y2Base - connectionOffset;
  } else if (sourceLayer.order > targetLayer.order) {
    // 如果源节点在下层，连线从图标顶部出发
    y1 = y1Base - connectionOffset;
    // 目标节点在上层，连线到图标底部
    y2 = y2Base + connectionOffset;
  } else {
    // 如果源节点和目标节点在同一层，使用中心点
    y1 = y1Base;
    y2 = y2Base;
  }

  // 使用贝塞尔曲线使连线更平滑，增加曲线长度
  // 计算垂直距离
  const verticalDistance = Math.abs(y2 - y1);
  // 计算水平距离
  const horizontalDistance = Math.abs(x2 - x1);
  // 计算控制点的偏移量，使曲线更长
  const curveOffset = Math.max(verticalDistance * 0.5, horizontalDistance * 0.3, 50);

  // 计算控制点
  let controlPoint1X: number, controlPoint1Y: number, controlPoint2X: number, controlPoint2Y: number;

  if (sourceLayer.order !== targetLayer.order) {
    // 如果节点在不同层，使用垂直控制点
    controlPoint1X = x1;
    controlPoint1Y = sourceLayer.order < targetLayer.order ? y1 + curveOffset : y1 - curveOffset;
    controlPoint2X = x2;
    controlPoint2Y = sourceLayer.order < targetLayer.order ? y2 - curveOffset : y2 + curveOffset;
  } else {
    // 如果节点在同一层，使用水平控制点
    controlPoint1X = x1 + (x2 - x1) / 3;
    controlPoint1Y = y1 - curveOffset;
    controlPoint2X = x1 + 2 * (x2 - x1) / 3;
    controlPoint2Y = y2 - curveOffset;
  }

  // 返回贝塞尔曲线路径
  return `M ${x1},${y1} C ${controlPoint1X},${controlPoint1Y} ${controlPoint2X},${controlPoint2Y} ${x2},${y2}`;
};

// 获取连线箭头样式
const getEdgeMarker = (edge: EdgeType): string => {
  if (!edge.status) return 'url(#arrowhead)';

  switch (edge.status) {
    case 'warning':
      return 'url(#arrowhead-warning)';
    case 'error':
      return 'url(#arrowhead-error)';
    default:
      return 'url(#arrowhead)';
  }
};

// 处理连线点击事件
const handleEdgeClick = (edge: EdgeType) => {
  emit('edgeClick', edge);
};

// 监听属性变化
watch(() => props.nodes, (newNodes) => {
  if (newNodes.length > 0) {
    internalNodes.value = newNodes;
  }
}, { deep: true });

watch(() => props.edges, (newEdges) => {
  if (newEdges.length > 0) {
    internalEdges.value = newEdges;
  }
}, { deep: true });

watch(() => props.layers, (newLayers) => {
  if (newLayers.length > 0) {
    internalLayers.value = newLayers;
  }
}, { deep: true });

// 生命周期钩子
onMounted(() => {
  if (props.autoLoad) {
    loadData();
  } else if (!props.nodes.length && !props.edges.length) {
    // 如果没有提供数据，使用默认数据
    useDefaultData();
  }

  // 计算初始 SVG 尺寸
  nextTick(() => {
    updateSvgSize();
    // 添加窗口大小变化监听
    window.addEventListener('resize', updateSvgSize);
  });

  // 添加动画和交互
  setTimeout(() => {
    addNodeAnimation();
    addInteractions();
  }, 500);
});

onUnmounted(() => {
  // 移除事件监听器
  const nodes = document.querySelectorAll('.node-circle, .node-server');

  nodes.forEach((node) => {
    if ((node as any)._mouseEnterHandler) {
      node.removeEventListener('mouseenter', (node as any)._mouseEnterHandler);
    }
    if ((node as any)._mouseLeaveHandler) {
      node.removeEventListener('mouseleave', (node as any)._mouseLeaveHandler);
    }
  });

  // 移除窗口大小变化监听
  window.removeEventListener('resize', updateSvgSize);
});

// 暴露方法给父组件
defineExpose({
  loadData,
  nodes: internalNodes,
  edges: internalEdges,
  layers: internalLayers
});
</script>

<style scoped>
.system-topology-container {
  width: 100%;
  height: 100%;
  position: relative;
  background-color: transparent;
  overflow: hidden;
}

.topology-background {
  width: 100%;
  height: 100%;
  position: relative;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.topology-layer {
  position: relative;
  height: 23.33%;
  width: 100%;
  background-image: url('../assets/系统构架-底层@3x.png');


  background-size: 100% 100%;
  background-repeat: no-repeat;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.layer-title {
  position: absolute;
  bottom: 4px;
  color: #fff;
  font-size: 16px;
  font-weight: bold;
  text-align: center;
  width: 100%;
}

.layer-nodes {
  display: flex;
  justify-content: space-around;
  width: 100%;
  padding: 0 10%;
}

.node-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
}

.node-circle,
.node-server {
  width: 46px;
  height: 46px;
  display: flex;
  justify-content: center;
  align-items: center;
  transition: all 0.3s;
  position: relative;
  cursor: pointer;
}

.node-circle:hover,
.node-circle.hover,
.node-server:hover,
.node-server.hover {
  transform: scale(1.05);
}

.node-circle img,
.node-server img {
  width: 60px;
  height: 60px;
  object-fit: contain;
}

.node-label {
  margin-top: 10px;
  color: #fff;
  font-size: 12px;
  text-align: center;
  max-width: 100px;
}

.topology-connections {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
}

.connection-line {
  stroke: #00eaff;
  stroke-width: 1.5;
  fill: none;
  stroke-dasharray: 2, 2;
  animation: dash 30s linear infinite;
  opacity: 0.3;
  cursor: pointer;
  pointer-events: stroke;
}

.flow-line {
  stroke-width: 2;
  fill: none;
  opacity: 0.5;
  animation: pulse 3s ease-in-out infinite, dataFlow 8s linear infinite;
  stroke-dasharray: 4, 12;
  stroke-linecap: round;
}

@keyframes pulse {
  0% {
    opacity: 0.2;
    stroke-width: 1.5;
  }
  50% {
    opacity: 0.5;
    stroke-width: 1.0;
  }
  100% {
    opacity: 0.2;
    stroke-width: 1.5;
  }
}

/* 不同状态的连线样式 */
.connection-line.status-warning,
.flow-line.status-warning {
  stroke: #ffcc00;
}

.connection-line.status-error,
.flow-line.status-error {
  stroke: #ff4d4f;
}

/* 不同状态的节点样式 */
.node-circle[data-status="warning"],
.node-server[data-status="warning"] {
  filter: drop-shadow(0 0 5px rgba(255, 204, 0, 0.7));
}

.node-circle[data-status="error"],
.node-server[data-status="error"] {
  filter: drop-shadow(0 0 5px rgba(255, 77, 79, 0.7));
}

.node-circle[data-status="offline"],
.node-server[data-status="offline"] {
  opacity: 0.5;
  filter: grayscale(0.8);
}

@keyframes dash {
  to {
    stroke-dashoffset: 1000;
  }
}

/* 添加数据流动效果 */
@keyframes dataFlow {
  0% {
    stroke-dashoffset: 0;
    stroke-dasharray: 0, 20;
    opacity: 0;
  }
  20% {
    opacity: 0.8;
  }
  100% {
    stroke-dashoffset: -1000;
    stroke-dasharray: 5, 15;
    opacity: 0;
  }
}

/* 添加高亮效果 */
.node-circle:hover img,
.node-server:hover img,
.node-circle.hover img,
.node-server.hover img {
  filter: brightness(1.2) drop-shadow(0 0 5px rgba(0, 234, 255, 0.7));
}

/* 添加节点浮动效果 */
@keyframes float {
  0% {
    transform: translateY(0) scale(1);
    filter: drop-shadow(0 0 3px rgba(0, 234, 255, 0.2));
  }
  50% {
    transform: translateY(-5px) scale(1.03);
    filter: drop-shadow(0 5px 8px rgba(0, 234, 255, 0.4));
  }
  100% {
    transform: translateY(0) scale(1);
    filter: drop-shadow(0 0 3px rgba(0, 234, 255, 0.2));
  }
}

/* 加载中样式 */
.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 10;
}

.loading-spinner {
  width: 50px;
  height: 50px;
  border: 3px solid rgba(0, 234, 255, 0.3);
  border-radius: 50%;
  border-top-color: #00eaff;
  animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

/* 错误消息样式 */
.error-message {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background-color: rgba(255, 77, 79, 0.8);
  color: white;
  padding: 10px 20px;
  border-radius: 4px;
  z-index: 5;
  max-width: 80%;
  text-align: center;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .node-circle,
  .node-server {
    width: 50px;
    height: 50px;
  }

  .node-circle img,
  .node-server img {
    width: 50px;
    height: 50px;
  }

  .node-label {
    font-size: 10px;
  }

  .layer-title {
    font-size: 14px;
  }
}
</style>
