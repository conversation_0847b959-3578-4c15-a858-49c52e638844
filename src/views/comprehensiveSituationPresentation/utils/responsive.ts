import { ref, onMounted, onUnmounted } from 'vue';

// 屏幕尺寸断点
export const SCREEN_SM = 640;
export const SCREEN_MD = 768;
export const SCREEN_LG = 1024;
export const SCREEN_XL = 1280;
export const SCREEN_2XL = 1536;

// 使用响应式布局钩子
export function useResponsive() {
  const screenWidth = ref(window.innerWidth);
  const screenHeight = ref(window.innerHeight);
  
  // 判断当前屏幕尺寸
  const isMobile = ref(screenWidth.value < SCREEN_MD);
  const isTablet = ref(screenWidth.value >= SCREEN_MD && screenWidth.value < SCREEN_LG);
  const isDesktop = ref(screenWidth.value >= SCREEN_LG);
  const isSmallScreen = ref(screenWidth.value < SCREEN_XL);
  
  // 计算缩放比例
  const scale = ref(1);
  
  // 更新屏幕尺寸和缩放比例
  const updateScreenSize = () => {
    screenWidth.value = window.innerWidth;
    screenHeight.value = window.innerHeight;
    
    // 更新设备类型判断
    isMobile.value = screenWidth.value < SCREEN_MD;
    isTablet.value = screenWidth.value >= SCREEN_MD && screenWidth.value < SCREEN_LG;
    isDesktop.value = screenWidth.value >= SCREEN_LG;
    isSmallScreen.value = screenWidth.value < SCREEN_XL;
    
    // 计算缩放比例 (基于1920px宽度的设计稿)
    if (screenWidth.value < 1366) {
      scale.value = screenWidth.value / 1366;
    } else if (screenWidth.value < 1920) {
      scale.value = screenWidth.value / 1920;
    } else {
      scale.value = 1;
    }
  };
  
  // 监听窗口大小变化
  onMounted(() => {
    updateScreenSize();
    window.addEventListener('resize', updateScreenSize);
  });
  
  onUnmounted(() => {
    window.removeEventListener('resize', updateScreenSize);
  });
  
  return {
    screenWidth,
    screenHeight,
    isMobile,
    isTablet,
    isDesktop,
    isSmallScreen,
    scale,
    updateScreenSize
  };
}

// 计算响应式尺寸
export function calcResponsiveSize(baseSize: number, scale: number): number {
  return Math.max(baseSize * scale, baseSize * 0.6); // 最小不低于60%
}

// 计算响应式字体大小
export function calcFontSize(baseSize: number, scale: number): string {
  const size = calcResponsiveSize(baseSize, scale);
  return `${size}px`;
}

// 计算响应式高度
export function calcHeight(baseHeight: number, scale: number): string {
  const height = calcResponsiveSize(baseHeight, scale);
  return `${height}px`;
}

// 计算响应式宽度
export function calcWidth(baseWidth: number, scale: number): string {
  const width = calcResponsiveSize(baseWidth, scale);
  return `${width}px`;
}
