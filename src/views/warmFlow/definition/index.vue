<template>
  <div>
    <el-form :model="queryForm" :inline="true">
      <el-form-item label="流程名称" prop="flowName">
        <el-input v-model="queryForm.flowName" style="width: 100%"></el-input>
      </el-form-item>
      <el-form-item label="流程编码" prop="flowCode">
        <el-input v-model="queryForm.flowCode" style="width: 100%"></el-input>
      </el-form-item>
      <el-form-item label="流程分类" prop="category">
        <el-input v-model="queryForm.category" style="width: 100%"></el-input>
      </el-form-item>
      <el-form-item label="发布状态" prop="isPublish">
        <el-select v-model="queryForm.isPublish" style="width: 100px" clearable>
          <el-option v-for="item in pushOptions" :key="item.value" :label="item.label" :value="item.value">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="success" @click="find4Page()">查询</el-button>
        <el-button type="primary" @click="openAddOrEditorDialog('新增')">新增</el-button>
      </el-form-item>
    </el-form>
    <el-table :data="pageData" stripe border>
      <el-table-column label="流程编码" prop="flowCode"></el-table-column>
      <el-table-column label="流程名称" prop="flowName"></el-table-column>
      <el-table-column label="流程版本" prop="version"></el-table-column>
      <el-table-column label="流程类别" prop="category"></el-table-column>
      <el-table-column label="是否发布" prop="isPublish">
        <template v-slot:="scope">
          {{ getLabel(pushOptions, scope.row.isPublish) }}
        </template>
      </el-table-column>
      <el-table-column label="创建时间" prop="createTime"></el-table-column>
      <el-table-column prop="操作" label="操作">
        <template v-slot:="scope">
          <el-button type="primary" size="small" link>
            <router-link :to="{
            path: '/warmFlow/definition/degign', query: {
              id: scope.row.id,
              disabled: scope.row.isPublish == 1 ? true : false,
            }
          }">流程图设计
            </router-link>
          </el-button>

          <el-button style="padding-left: 6px;" v-if="scope.row.isPublish == 0" link type="primary" size="small"
            @click="openAddOrEditorDialog('编辑', scope.row)">编辑</el-button>
          <el-button v-if="scope.row.isPublish == 0" link type="primary" size="small"
            @click="editFlowDel(scope.row)">删除</el-button>
          <el-button v-if="scope.row.isPublish == 0" link type="primary" size="small"
            @click="editFlowPublish(scope.row)">发布</el-button>
          <el-button v-if="scope.row.isPublish == 1" link type="primary" size="small"
                     @click="unPublish(scope.row)">取消发布</el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination style="margin-top: 1rem;" background layout="prev, pager, next,jumper,sizes" :total="pageInfo.total"
      :page-sizes="[10, 20, 30, 40]" :page-size="pageInfo.pageSize" @size-change="sizeChange"
      @current-change="currentChange" />


    <el-dialog v-model="showDialog" :title="title">
      <el-form :model="editFrom">
        <el-row :gutter="20" :span="12">
          <el-col>
            <el-form-item>
              <el-input v-model="editFrom.flowName">流程名称</el-input>
            </el-form-item>

          </el-col>
          <el-col>
            <el-form-item v-model="editFrom.flowCode">流程编码</el-form-item>
          </el-col>
        </el-row>
        <el-row></el-row>
        <el-row></el-row>
        <el-form-item></el-form-item>
        <el-form-item></el-form-item>
        <el-form-item></el-form-item>
        <el-form-item></el-form-item>
      </el-form>
    </el-dialog>


    <el-dialog @close="resetForm(ruleFormRef, 'close')" v-model="addOrEditorDialogVisible"
      :title="addOrEditorDialogTitle" width="40%" style="padding-right: 3rem;">
      <el-form ref="ruleFormRef" :model="ruleForm" :rules="rules" label-width="120px" class="demo-ruleForm"
        :size="formSize" status-icon>
        <el-form-item label="流程名称" prop="flowName">
          <el-input v-model="ruleForm.flowName" />
        </el-form-item>
        <el-form-item label="流程编码" prop="flowCode">
          <el-input v-model="ruleForm.flowCode" />
        </el-form-item>
        <el-form-item label="流程类别" prop="category">
          <el-input v-model="ruleForm.category" />
        </el-form-item>
        <el-form-item label="版本号" prop="version">
          <el-input v-model="ruleForm.version" />
        </el-form-item>
        <el-form-item label="表单地址" prop="formPath">
          <el-input v-model="ruleForm.formPath" />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="resetForm(ruleFormRef, 'close')">取消</el-button>
          <el-button type="primary" @click="submitForm(ruleFormRef)">
            保存
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>

</template>

<script setup lang="ts">

import { FlowDefinition, FlowDefinitionParam, list4Page, definitionPublish,
  definitionSave,definitionUnPublish } from "@/views/warmFlow/definition/api/flowDefinition";
import { onMounted, ref, reactive, toRef } from "vue";
import { useRouter } from 'vue-router';
import { ElMessage } from 'element-plus'
import type { FormInstance, FormRules } from 'element-plus'

const showDialog = ref(false);
const title = ref("新增");
const useRouter_ = useRouter();
const editFrom = ref<FlowDefinition>({
  activityStatus: null,
  category: "",
  createTime: null,
  delFlag: "",
  ext: "",
  flowName: "",
  formCustom: "",
  formPath: "",
  isPublish: 0,
  listenerPath: "",
  listenerType: "",
  tenantId: "",
  updateTime: null,
  version: "",
  xmlString: "",
  id: null,
  flowCode: ""
});

const queryForm = ref<FlowDefinitionParam>({
  "pageNum": 1,
  "pageSize": 10, "flowName": "", "flowCode": "", "category": "", "isPublish": null, "version": ""
}
);


const editFlowDefinition = (row: any) => {
  console.log(row.id);
  let p = {
    definitionId: String(row.id),
  }
  console.log(p);
  useRouter_.push({ name: "flow_design", params: p });

}

const createFlowDefinition = () => {

}

const sizeChange = (size: number) => {
  pageInfo.value.pageSize = size;
  pageInfo.value.pageNum = 1;
  find4Page();
}

const currentChange = (current: number) => {
  pageInfo.value.pageNum = current;
  pageInfo.value.pageSize = 2;
  find4Page();
}

const find4Page = () => {
  queryForm.value.pageNum = pageInfo.value.pageNum;
  queryForm.value.pageSize = pageInfo.value.pageSize;
  list4Page(queryForm.value).then(res => {
    console.log(res);
    pageData.value = res.data;
    pageInfo.value.total = Number.parseInt(res.total);
  })
}


onMounted(() => {
  find4Page();
});

let pageData = ref<Array<FlowDefinition>>([]);
const pageInfo = ref({
  pageNum: 1,
  pageSize: 10,
  total: 0,
});
const pushOptions = [
  { value: 1, label: "已发布" },
  { value: 0, label: "未发布" }
];
// const selection = [10, 20, 30, 50];

const getLabel = (array, value) => {
  if (array && array.length > 0 && !isEmpty(value)) {
    let type = Object.prototype.toString.call(value).slice(8, -1);
    let arr = [];
    if ("String" === type) {
      arr = value.split(",");
    } else {
      arr.push(value);
    }
    let lables = [];
    arr.forEach(item => {
      let node = array.find(data => data.value == item);
      if (node) {
        lables.push(node.label);
      }
    });
    return lables.join(",");
  }
  return "";
}
const isEmpty = (value) => {
  let type;
  if (value == null) { // 等同于 value === undefined || value === null
    return true;
  }
  type = Object.prototype.toString.call(value).slice(8, -1);
  switch (type) {
    case "String":
      return !$.trim(value);
    case "Array":
      return !value.length;
    case "Object":
      return $.isEmptyObject(value); // 普通对象使用 for...in 判断，有 key 即为 false
    default:
      return false; // 其他对象均视作非空
  }
}

const addOrEditorDialogVisible = ref(false);
const addOrEditorDialogTitle = ref('');
interface RuleForm {
  "flowCode": String,
  "flowName": String,
  "category": String,
  "version": String,
  "formPath": String,
}
const currentRow = ref({});
const formSize = ref('default')
const ruleFormRef = ref<FormInstance>()
const ruleForm = reactive<RuleForm>({
  flowCode: '',
  flowName: '',
  category: '',
  version: '',
  formPath: '',
})
const rules = reactive<FormRules<RuleForm>>({
  flowCode: [
    { required: true, message: '请输入流程编码', trigger: 'blur' },
  ],
  flowName: [
    { required: true, message: '请输入流程名称', trigger: 'blur' },
  ],
  formPath: [
    { required: true, message: '请输入表单地址', trigger: 'blur' },
  ],
})
const isObjectEmpty = obj => Object.keys(obj).length === 0;
const submitForm = async (formEl: FormInstance | undefined) => {
  if (!formEl) return
  await formEl.validate((valid, fields) => {
    if (valid) {
      console.log('submit!', ruleForm);
      let tmpForm = {};
      if (!isObjectEmpty(currentRow.value)) {
        tmpForm = { ...currentRow.value, ...ruleForm };
      } else {
        tmpForm = { ...ruleForm };
      }
      definitionSave(tmpForm).then(res => {
        if (res['data']) {
          resetForm(ruleFormRef.value);
          addOrEditorDialogVisible.value = false;
          ElMessage.success('保存成功');
          find4Page();
        } else {
          ElMessage.error('保存失败');
        }
      }).catch(err => {
        ElMessage.error('保存失败');
      })

    } else {
      console.log('error submit!', fields)
    }
  })
}

const resetForm = (formEl: FormInstance | undefined, isClose?: String) => {
  if (!formEl) return
  formEl.resetFields()
  ruleForm['flowCode'] = "";
  ruleForm['flowName'] = "";
  ruleForm['category'] = "";
  ruleForm['version'] = "";
  ruleForm['formPath'] = "";
  currentRow.value = {};
  if (isClose == 'close') {
    addOrEditorDialogVisible.value = false;
  }
}

const openAddOrEditorDialog = (type: String, row?: Object) => {
  resetForm(ruleFormRef.value);
  if (type == '编辑') {
    addOrEditorDialogVisible.value = true;
    addOrEditorDialogTitle.value = '编辑流程';
    ruleForm['flowCode'] = row['flowCode'];
    ruleForm['flowName'] = row['flowName'];
    ruleForm['category'] = row['category'];
    ruleForm['version'] = row['version'];
    ruleForm['formPath'] = row['formPath'];
    console.log(row);
    currentRow.value = row;
  } else if (type == '新增') {
    currentRow.value = {};
    addOrEditorDialogTitle.value = '新增流程';
    addOrEditorDialogVisible.value = true;
    resetForm(ruleFormRef.value);
  }
}

const editFlowDel = (item) => {
  console.log(item);
}

const editFlowPublish = (item) => {
  console.log(item);
  definitionPublish(item.id).then(res => {
    console.log(res);
    if (res['data']) {
      ElMessage({
        showClose: false,
        message: '发布成功',
        type: 'success',
      })
    }
    find4Page();
  });
}

const unPublish = (item) => {
  definitionUnPublish(item.id).then(res => {
    console.log(res);
    if (res['data']) {
      ElMessage({
        showClose: false,
        message: '取消发布成功',
        type: 'success',
      })
    }
    find4Page();
  });
}
</script>
