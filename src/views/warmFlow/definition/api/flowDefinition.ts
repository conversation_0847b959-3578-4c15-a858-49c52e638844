import { http } from "@/utils/http";
import { ServerNames } from "@/utils/http/serverNames";

const basePath = `${ServerNames.appServerUrl}`;

export interface Page {
  pageNum: number;
  pageSize: number;
}

export interface FlowDefinition {
  // 主键
  id: number | null;
  // 创建时间
  createTime: Date | null;
  // 更新时间
  updateTime: Date | null;
  // 租户ID
  tenantId: string | null;
  // 删除标记
  delFlag: string | null;
  // 流程编码
  flowCode: string | null;
  // 流程名称
  flowName: string | null;
  // 流程类别
  category: string | null;
  // 流程版本
  version: string | null;
  // 是否发布（0未开启 1开启）
  isPublish: number | null;
  // 审批表单是否自定义（Y是 2否）
  formCustom: string | null;
  // 审批表单路径
  formPath: string | null;
  // 流程激活状态（0挂起 1激活）
  activityStatus: number | null;
  // 监听器类型
  listenerType: string | null;
  // 监听器路径
  listenerPath: string | null;
  // 扩展字段，预留给业务系统使用
  ext: string | null;
  // 流程定义XML字符串
  xmlString: string | null;
}

export interface FlowDefinitionParam extends Page {
  flowCode: string;
  flowName: string;
  category: string;
  version: string;
  isPublish: number;
}


//流程定义分页查询
const list4Page = (params: FlowDefinitionParam) =>
  http.postJson<RestPageResult<Array<FlowDefinition>>>(
    `${basePath}/jd/flow/definition/listPage`,
    params
  );

//发布流程定义
const definitionPublish = (id: String | Number) =>
  http.get(
    `${basePath}/jd/flow/definition/publish?id=${id}`
  );

// 流程定义保存
const definitionSave = (params: Object) =>
  http.postJson<RestPageResult<Array<FlowDefinition>>>(
    `${basePath}/jd/flow/definition/save`,
    params
  );

//发布流程定义
const definitionUnPublish = (id: String | Number) =>
  http.get(
    `${basePath}/jd/flow/definition/unPublish?id=${id}`
  );


export {
  list4Page,
  definitionPublish,
  definitionSave,
  definitionUnPublish
};
