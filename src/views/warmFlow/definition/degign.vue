<template>
  <div class="container" ref="container">
    <iframe ref="iframe" :src="iframeUrl" frameborder="0" width="100%" height="100%"></iframe>
  </div>
</template>

<script setup name="WarmFlow">
const {proxy} = getCurrentInstance();
import {getCurrentInstance, onBeforeMount, onMounted, ref} from 'vue';
import {useRoute} from "vue-router"
import { getToken } from "@/utils/auth";
const stg = ref({
  definitionId: "",
  disabled: false,
});
const route = useRoute();
// import.meta.env.VITE_APP_BASE_API:  前端地址的前缀如dev-api
// definitionId: 为需要查询的流程定义id
// disabled: 为是否可编辑, 例如：查看的时候不可编辑，不可保存
const iframeUrl = ref(`http:/rest/app-server/warm-flow-ui/index.html?id=` + stg.value.definitionId + `&disabled=` + stg.value.disabled);

const iframeLoaded = () => {
  // iframe监听组件内设计器保存事件
  window.onmessage = (event) => {
    switch (event.data.method) {
      case "close":
        close();
        break;
    }
  }
};

/** 关闭按钮 */
function close() {
  const obj = {path: "/flow/definition"};
  proxy.$tab.closeOpenPage(obj);
}


onBeforeMount(() => {

});

onMounted(() => {
  console.info(route.query)
  stg.value.definitionId = route.query.id;
  stg.value.disabled = route.query.disabled;
  console.info("stg:" + stg);
  iframeUrl.value = `http:/rest/app-server/warm-flow-ui/index.html?id=` + stg.value.definitionId + `&disabled=` + stg.value.disabled+`&Authorization=`+getToken().accessToken;
  console.info("url:" + iframeUrl.value);
  iframeLoaded();
});

</script>

<style scoped>
.container {
  width: 100%;
  height: calc(100vh - 84px);
}
</style>
