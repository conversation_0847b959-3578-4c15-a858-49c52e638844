<template>
  <div class="flex flex-col items-center justify-center h-full w-full">
    <div class="pb-1.5 text-xs">系统加载中...</div>
    <el-progress
      :percentage="100"
      :stroke-width="15"
      striped
      striped-flow
      class="w-60"
      :duration="18"
      :show-text="false"
    />
  </div>
</template>

<script lang="ts" setup>
import { getQueryMap } from "@pureadmin/utils";
import { removeToken } from "@/utils/auth";
import { message } from "@/utils/message";
import { router } from "@/router";
import { UserSessionInfo, verifyToken } from "@/api/user";
import { ResultStatus } from "@/utils/http/types";
import { useUserStoreHook } from "@/store/modules/user";
import { getTopMenu, initRouter } from "@/router/utils";

const params = getQueryMap(location.href);
//必填参数
const must = "token";
//不传递的参数
const excludeParams = ["token", "path"];

const doSso = () => {
  if (Object.keys(params).includes(must)) {
    removeToken();
    const token = params[must];
    verifyToken({ token: token }).then(res => {
      if (res.status == ResultStatus.Success) {
        const resToken: string = res.data;
        const userSession: UserSessionInfo = JSON.parse(res.msg);
        //存储session和token信息
        useUserStoreHook().dealSessionInfo(userSession, resToken);

        //初始化路由
        const destPage = params["path"];
        initRouter().then(() => {
          if (destPage != null && destPage.length > 0) {
            window.location.replace(joinLocationUrl(params, destPage));
          } else {
            router.push(getTopMenu(true).path);
          }
        });
      } else {
        router.push("/login");
      }
    });
  } else {
    message("非法单点登录请求！", {
      type: "error",
      showClose: true,
      duration: 0
    });
  }
};
doSso();

//组织要跳转的地址
const joinLocationUrl = (params: object, destPage: string): string => {
  let baseUrl = `${location.origin}${location.pathname}#/redirect${destPage}`;
  const keyArray = Object.keys(params).filter(k => !excludeParams.includes(k));
  for (let i = 0; i < keyArray.length; i++) {
    if (i == 0) {
      baseUrl = baseUrl + `?${keyArray[i]}=${params[keyArray[i]]}`;
    } else {
      baseUrl = baseUrl + `&${keyArray[i]}=${params[keyArray[i]]}`;
    }
  }
  return baseUrl;
};
</script>
