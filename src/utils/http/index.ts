import Axios, {
  AxiosInstance,
  AxiosRequestConfig,
  CustomParamsSerializer
} from "axios";
import {
  HttpRequestMethod,
  HttpResponseType,
  PureHttpError,
  PureHttpRequestConfig,
  PureHttpResponse,
  RequestMethods,
  ResultStatus
} from "./types";
import qs from "qs";
import NProgress from "../progress";
import { formatToken, getToken } from "@/utils/auth";
import { useUserStoreHook } from "@/store/modules/user";
import { ServerNames } from "@/utils/http/serverNames";
import { getFingerId } from "@/utils/IdUtil";
import { ElNotification } from "element-plus";

// 相关配置请参考：www.axios-js.com/zh-cn/docs/#axios-request-config-1
const defaultConfig: AxiosRequestConfig = {
  // 请求超时时间 单位ms
  timeout: 10000,
  headers: {
    Accept: "application/json, text/plain, */*",
    "Content-Type": "application/json",
    "X-Requested-With": "XMLHttpRequest"
  },
  // 数组格式参数序列化（https://github.com/axios/axios/issues/5142）
  paramsSerializer: {
    serialize: qs.stringify as unknown as CustomParamsSerializer
  }
};

class PureHttp {
  constructor() {
    this.httpInterceptorsRequest();
    this.httpInterceptorsResponse();
  }

  /** token过期后，暂存待执行的请求 */
  private static requests = [];

  /** 防止重复刷新token */
  private static isRefreshing = false;

  /** 初始化配置对象 */
  private static initConfig: PureHttpRequestConfig = {};

  /** 保存当前Axios实例对象 */
  private static axiosInstance: AxiosInstance = Axios.create(defaultConfig);

  /** 重连原始请求 */
  private static retryOriginalRequest(config: PureHttpRequestConfig) {
    return new Promise(resolve => {
      PureHttp.requests.push((token: string) => {
        config.headers["Authorization"] = formatToken(token);
        resolve(config);
      });
    });
  }

  /**
   * 配置HTTP请求拦截器
   * 本函数主要用于在请求发送之前进行一系列的处理和配置
   */
  private httpInterceptorsRequest(): void {
    //刷新过期Token
    function refreshToken(config: PureHttpRequestConfig) {
      return new Promise(resolve => {
        const data = getToken();
        if (data) {
          const now = new Date().getTime();
          const expired = parseInt(data.expires) - now <= 0;
          if (expired) {
            if (!PureHttp.isRefreshing) {
              PureHttp.isRefreshing = true;
              // 触发token刷新钩子
              useUserStoreHook()
                .handRefreshToken({ refreshToken: data.refreshToken })
                .then(res => {
                  const token = res.data;
                  config.headers["Authorization"] = formatToken(token);
                  PureHttp.requests.forEach(cb => cb(token));
                  PureHttp.requests = [];
                })
                .finally(() => {
                  PureHttp.isRefreshing = false;
                });
            }
            resolve(PureHttp.retryOriginalRequest(config));
          } else {
            config.headers["Authorization"] = formatToken(data.accessToken);
            resolve(config);
          }
        } else {
          resolve(config);
        }
      });
    }

    //注册请求拦截
    PureHttp.axiosInstance.interceptors.request.use(
      async (config: PureHttpRequestConfig): Promise<any> => {
        //设置请求基础路径
        config.baseURL = ServerNames.baseUrl;

        // 开启进度条动画
        NProgress.start();
        // 优先判断post/get等方法是否传入回调，否则执行初始化设置等回调
        if (typeof config.beforeRequestCallback === "function") {
          config.beforeRequestCallback(config);
          return config;
        }
        if (PureHttp.initConfig.beforeRequestCallback) {
          PureHttp.initConfig.beforeRequestCallback(config);
          return config;
        }

        /** 请求白名单，放置一些不需要token的接口（通过设置请求白名单，防止token过期后再请求造成的死循环问题） */
        const whiteList = ["/token", "/login"];

        //处理客户端指纹Id
        config.headers["Client-Id"] = getFingerId();

        return whiteList.some(v => config.url.indexOf(v) > -1)
          ? config
          : refreshToken(config);
      },
      error => {
        return Promise.reject(error);
      }
    );
  }

  //响应拦截
  private httpInterceptorsResponse(): void {
    const instance = PureHttp.axiosInstance;
    instance.interceptors.response.use(
      (response: PureHttpResponse) => {
        const $config = response.config;

        // 关闭进度条动画
        NProgress.done();

        if ($config.responseType === HttpResponseType.Blob) {
          //处理数据传输响应
          return this.dealBlobResponse(response);
        } else {
          //处理http响应
          this.dealHttpStatus(response, "");

          //处理内部异常响应
          this.dealInternalStatus(response);

          // 优先判断post/get等方法是否传入回调，否则执行初始化设置等回调
          if (typeof $config.beforeResponseCallback === "function") {
            $config.beforeResponseCallback(response);
            return response.data;
          }
          if (PureHttp.initConfig.beforeResponseCallback) {
            PureHttp.initConfig.beforeResponseCallback(response);
            return response.data;
          }
          return response.data;
        }
      },
      (error: PureHttpError) => {
        const $error = error;
        $error.isCancelRequest = Axios.isCancel($error);

        // 关闭进度条动画
        NProgress.done();

        //处理http响应
        this.dealHttpStatus(error.response, error.message);

        // 所有的响应异常 区分来源为取消请求/非取消请求
        return Promise.reject($error);
      }
    );
  }

  //统一处理Http响应状态码
  private dealHttpStatus(response: PureHttpResponse, errMsg: string) {
    const statusCode = response.status;
    if (statusCode === 203) {
      //退出到登录界面
      useUserStoreHook().logOut();
      ElNotification({
        title: "提示",
        message: "长时间未操作，请重新登录!",
        type: "success"
      });
    } else if (statusCode !== 200) {
      //其他异常输出到控制台
      const message = `服务端响应异常(${statusCode}):${response.config.url},${errMsg}`;
      console.error(
        "%c" + message,
        "background:#ff0000;color:#fff;border-radius:2px;padding:2px 5px"
      );
    }
  }

  //统一处理内部响应状态码 RestResult.status
  private dealInternalStatus(response: PureHttpResponse) {
    const responseData = response.data;
    const status = (
      "" + (responseData.status ?? responseData.code)
    ).toLocaleLowerCase();
    const msg = responseData.msg ?? responseData.message ?? responseData.data;

    if (status === ResultStatus.Failure) {
      //需要提示的异常
      ElNotification({
        title: "提示",
        message: msg,
        type: "warning",
        duration: 5 * 1000,
        showClose: true
      });
    } else if (status !== ResultStatus.Success) {
      const message = `系统内部异常(${response.config.url}):${msg}`;
      console.error(
        "%c" + message,
        "background:#ff0000;color:#fff;border-radius:2px;padding:2px 5px"
      );
    }
  }

  //处理数据传输响应 自动下载文件
  private dealBlobResponse(response: PureHttpResponse): Promise<any> {
    return new Promise<any>(resolve => {
      //读取blob数据
      const blob = response.data;
      const reader = new FileReader();
      reader.readAsText(blob);

      //获取文件名
      const fileName = this.getBlobFileName(response);

      //植入到a元素中
      const url = window.URL.createObjectURL(new Blob([blob]));
      const a = document.createElement("a");
      a.style.display = "none";
      a.download = fileName;
      a.href = url;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      resolve(response);
    });
  }

  //获取下载的文件名
  private getBlobFileName(response: PureHttpResponse): string {
    const resConfig = response.config;

    // 辅助函数：从对象中安全获取文件名
    function getFileNameFromObject(
      obj: any,
      key: string
    ): string | qs.ParsedQs | (string | qs.ParsedQs)[] | undefined {
      if (typeof obj === "string") {
        try {
          return JSON.parse(obj)?.[key];
        } catch (err) {
          try {
            return qs.parse(obj)?.[key];
          } catch (parseErr) {
            console.error("Failed to parse object:", parseErr);
            return null;
          }
        }
      } else {
        return obj?.[key] || null;
      }
    }

    // 获取文件名
    let fileName: string | qs.ParsedQs | (string | qs.ParsedQs)[] = null;

    if (resConfig.method === HttpRequestMethod.Get) {
      fileName = getFileNameFromObject(resConfig.params, "filename");
    } else if (resConfig.method === HttpRequestMethod.Post) {
      fileName = getFileNameFromObject(resConfig.data, "filename");
    }

    if (fileName != null) {
      return fileName as string;
    }

    // 从响应头中获取文件名
    if (
      Object.prototype.hasOwnProperty.call(
        response.headers,
        "content-disposition"
      )
    ) {
      const contentDisposition = response.headers["content-disposition"];
      const match = contentDisposition.match(/filename=["']?([^"'\s]+)["']?/);
      if (match) {
        try {
          return decodeURIComponent(match[1]);
        } catch (uriError) {
          console.error("Failed to decode URI component:", uriError);
        }
      }
    }

    return "down_file";
  }

  //通用请求工具函数
  public request<T>(
    method: RequestMethods,
    url: string,
    param?: AxiosRequestConfig,
    axiosConfig?: PureHttpRequestConfig
  ): Promise<T> {
    const config = {
      method,
      url,
      ...param,
      ...axiosConfig
    } as PureHttpRequestConfig;

    //单独处理自定义请求/响应回调
    return new Promise((resolve, reject) => {
      PureHttp.axiosInstance
        .request(config)
        .then((response: undefined) => {
          resolve(response);
        })
        .catch(error => {
          reject(error);
        });
    });
  }

  //发送Get请求
  public get<T, P>(
    url: string,
    params?: AxiosRequestConfig<T>,
    config?: PureHttpRequestConfig
  ): Promise<P> {
    return this.request<P>(HttpRequestMethod.Get, url, params, config);
  }

  //发送Post请求
  public post<T, P>(
    url: string,
    params?: AxiosRequestConfig<T>,
    config?: PureHttpRequestConfig
  ): Promise<P> {
    return this.request<P>(HttpRequestMethod.Post, url, params, config);
  }

  //发送postJson请求，参数json格式放置于请求body中
  public postJson<P>(url: string, data?: Object): Promise<P> {
    return this.post<any, P>(url, { data });
  }

  //发送下载请求，请求为request参数
  public postBlob<P>(url: string, data?: Object): Promise<P> {
    return this.post<any, P>(
      url,
      { data },
      { responseType: HttpResponseType.Blob }
    );
  }

  //发送下载请求，请求参数为json格式
  public postBlobWithJson<P>(url: string, data?: Object): Promise<P> {
    return this.post<any, P>(
      url,
      { data },
      {
        responseType: HttpResponseType.Blob,
        headers: {
          "Content-Type": "application/json"
        }
      }
    );
  }

  //multipart/form-data 上传文件  post请求
  public postUpdateFile<P>(url: string, data?: Object): Promise<P> {
    return this.post<any, P>(
      url,
      { data },
      {
        headers: {
          "Content-Type": "multipart/form-data"
        }
      }
    );
  }
}

export const http = new PureHttp();
