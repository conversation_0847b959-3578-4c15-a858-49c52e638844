@use "element-plus/theme-chalk/src/dark/css-vars.scss" as *;

/* 暗黑模式适配 */
html.dark {
  /* 自定义深色背景颜色 */
  // --el-bg-color: #020409;
  $border-style: #303030;
  $color-white: #fff;
  $sys-bg-color: #141414;

  --el-text-color-primary: $color-white !important;
  --el-text-color-regular: $color-white !important;

  .navbar,
  .tags-view,
  .contextmenu,
  .sidebar-container,
  .horizontal-header,
  .sidebar-logo-container,
  .horizontal-header .el-sub-menu__title,
  .horizontal-header .submenu-title-noDropdown {
    background: var(--el-bg-color) !important;
  }

  .app-main {
    background: $sys-bg-color !important;
  }

  .frame {
    filter: invert(0.9) hue-rotate(180deg);
  }

  /* 标签页 */
  .tags-view {
    .arrow-left,
    .arrow-right {
      border-right: 1px solid $border-style;
      box-shadow: none;
    }

    .arrow-right {
      border-left: 1px solid $border-style;
    }
  }

  /* 项目配置面板 */
  .right-panel-items {
    .el-divider__text {
      --el-bg-color: var(--el-bg-color);
    }

    .el-divider--horizontal {
      border-top: none;
    }
  }

  /* element-plus */
  .el-table__cell {
    background: var(--el-bg-color);
  }

  .el-card {
    --el-card-bg-color: var(--el-bg-color);

    // border: none !important;
  }

  .el-backtop {
    --el-backtop-bg-color: var(--el-color-primary-light-9);
    --el-backtop-hover-bg-color: var(--el-color-primary);
  }

  .el-dropdown-menu__item:not(.is-disabled):hover {
    background: transparent;
  }

  /* 全局覆盖element-plus的el-dialog、el-drawer、el-message-box、el-notification组件右上角关闭图标的样式，表现更鲜明 */
  .el-icon {
    &.el-dialog__close,
    &.el-drawer__close,
    &.el-message-box__close,
    &.el-notification__closeBtn {
      &:hover {
        color: rgb(255 255 255 / 85%) !important;
        background-color: rgb(255 255 255 / 12%);

        .pure-dialog-svg {
          color: rgb(255 255 255 / 85%) !important;
        }
      }
    }
  }

  /* 克隆并自定义 ElMessage 样式，不会影响 ElMessage 原本样式，在 src/utils/message.ts 中调用自定义样式 ElMessage 方法即可，非暗黑模式在 src/style/element-plus.scss 文件进行了适配 */
  .pure-message {
    background-color: rgb(36 37 37) !important;
    background-image: initial !important;
    box-shadow: rgb(13 13 13 / 12%) 0 3px 6px -4px,
      rgb(13 13 13 / 8%) 0 6px 16px 0, rgb(13 13 13 / 5%) 0 9px 28px 8px !important;

    & .el-message__content {
      color: $color-white !important;
      pointer-events: all !important;
      background-image: initial !important;
    }

    & .el-message__closeBtn {
      &:hover {
        color: rgb(255 255 255 / 85%);
        background-color: rgb(255 255 255 / 12%);
      }
    }
  }

  /* 自定义菜单搜索样式 */
  .pure-search-dialog {
    .el-dialog__footer {
      box-shadow: 0 -1px 0 0 #555a64, 0 -3px 6px 0 rgb(69 98 155 / 12%);
    }

    .search-footer {
      .search-footer-item {
        color: rgb(235 235 235 / 60%);

        .icon {
          box-shadow: none;
        }
      }
    }
  }

  /* ReSegmented 组件 */
  .pure-segmented {
    color: rgb(255 255 255 / 65%);
    background-color: #000;

    .pure-segmented-item-selected {
      background-color: #1f1f1f;
    }

    .pure-segmented-item-disabled {
      color: rgb(255 255 255 / 25%);
    }
  }

  .avue-crud {
    .el-table {
      th.el-table__cell {
        color: $color-white;
        background-color: #1d1d1d !important;
      }
    }
  }

  .avue-crud__header {
    background-color: $sys-bg-color !important;
  }

  .avue-dialog {
    .el-dialog__title {
      color: $color-white;
    }

    .el-drawer__header {
      color: $color-white;
      border-bottom: 1px solid $border-style;
    }

    .el-dialog__header {
      border-bottom: 1px solid $border-style;
    }
  }

  .avue-group {
    .el-collapse-item__wrap {
      border-color: $sys-bg-color;
    }

    .el-collapse {
      border-color: $sys-bg-color;
    }
  }

  .avue-dialog__footer {
    background-color: $sys-bg-color;
    border-top: 1px solid $border-style;
  }

  .avue--detail {
    .el-form-item__label {
      background: $sys-bg-color;
    }

    .el-form-item__content {
      background-color: $sys-bg-color;
      border-left: 1px solid $border-style;
    }

    .el-input.is-disabled {
      .el-input__wrapper {
        background-color: $sys-bg-color;
      }

      .el-input__inner {
        color: $color-white;
        background-color: $sys-bg-color;
      }
    }

    .el-row {
      border-top: 1px solid $border-style;
      border-left: 1px solid $border-style;
    }

    .el-col {
      border-right: 1px solid $border-style;
      border-bottom: 1px solid $border-style;
    }

    .el-form-item {
      background: $sys-bg-color;
    }

    .el-textarea.is-disabled {
      .el-textarea__inner {
        color: $color-white;
        background: $sys-bg-color;
      }
    }
  }

  .avue-crud__tip {
    background-color: $sys-bg-color;
    border: 1px solid $border-style;
  }

  .data-box {
    .item-icon {
      color: rgb(235 235 235 / 60%);
    }

    .item-info {
      background-color: rgb(255 255 255 / 12%);
      border: 1px solid $border-style;

      .info {
        color: $color-white;
      }
    }
  }
}
