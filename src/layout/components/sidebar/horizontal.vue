<script setup lang="ts">
import Search from "../search/index.vue";
import Notice from "../notice/index.vue";
import SidebarItem from "./sidebarItem.vue";
import { isAllEmpty } from "@pureadmin/utils";
import { computed, nextTick, ref } from "vue";
import { useNav } from "@/layout/hooks/useNav";
import { usePermissionStoreHook } from "@/store/modules/permission";
import tShirtFill from "@iconify-icons/ri/t-shirt-fill";
import AvatarMenu from "@/layout/components/sidebar/AvatarMenu.vue";
import ThemeSwitch from "@/layout/components/setting/components/ThemeSwitch.vue";

const menuRef = ref();

const { route, title, subTitle, backTopMenu, onPanel } = useNav();

const defaultActive = computed(() =>
  !isAllEmpty(route.meta?.activePath) ? route.meta.activePath : route.path
);

nextTick(() => {
  menuRef.value?.handleResize();
});
</script>

<template>
  <div class="horizontal-header">
    <!--左侧LOGO以及名称-->
    <div class="horizontal-header-left" @click="backTopMenu">
      <img src="/sop.svg" alt="logo" width="45" />
      <div>
        <span class="font-bold">{{ title }}</span>
        <span class="text-[11px] flex-c">{{ subTitle }}</span>
      </div>
    </div>

    <!--系统横向菜单-->
    <el-menu
      router
      ref="menuRef"
      mode="horizontal"
      class="horizontal-header-menu"
      :default-active="defaultActive"
    >
      <sidebar-item
        v-for="route in usePermissionStoreHook().wholeMenus"
        :key="route.path"
        :item="route"
        :base-path="route.path"
      />
    </el-menu>

    <!-- 右侧固定菜单 -->
    <div class="horizontal-header-right space-x-1">
      <!-- 菜单搜索 -->
      <Search />

      <!-- 主题切换 -->
      <theme-switch />

      <!-- 通知 -->
      <Notice id="header-notice" />

      <!-- 界面设置 -->
      <span
        class="set-icon navbar-bg-hover"
        title="打开界面设置"
        @click="onPanel"
      >
        <IconifyIconOffline :icon="tShirtFill" />
      </span>

      <!-- 用户头像菜单 -->
      <avatar-menu />
    </div>
  </div>
</template>

<style lang="scss" scoped>
:deep(.el-loading-mask) {
  opacity: 0.45;
}
</style>
