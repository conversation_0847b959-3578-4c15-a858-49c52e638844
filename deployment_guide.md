# 前端项目部署指南

本文档为非开发人员（运维或部署人员）提供详细的部署指导，帮助您顺利完成前端项目的部署工作。

## 目录

1. [部署前准备](#1-部署前准备)
   - [1.1 服务器要求](#11-服务器要求)
   - [1.2 部署模式说明](#12-部署模式说明)
   - [1.3 构建环境说明](#13-构建环境说明仅供参考)
2. [部署文件说明](#2-部署文件说明)
   - [2.1 静态资源文件](#21-静态资源文件)
   - [2.2 文件结构说明](#22-文件结构说明)
3. [部署流程](#3-部署流程)
   - [3.1 接收前端构建产物](#31-接收前端构建产物)
   - [3.2 部署到服务器](#32-部署到服务器)
4. [Nginx 配置](#4-nginx-配置)
   - [4.1 Nginx 配置示例](#41-nginx-配置示例)
5. [常见问题及解决方案](#5-常见问题及解决方案)
   - [5.1 白屏问题](#51-白屏问题)
   - [5.2 API 请求失败](#52-api-请求失败)
   - [5.3 性能问题](#53-性能问题)
6. [更新部署](#6-更新部署)
7. [联系支持](#7-联系支持)

## 1. 部署前准备

### 1.1 服务器要求

- **操作系统**：支持 Linux、Windows Server 或其他支持 Nginx 的服务器操作系统
- **Web服务器**：Nginx（推荐 1.18 或更高版本）
- **磁盘空间**：至少 1GB 可用空间（用于部署静态文件）
- **内存**：至少 2GB RAM（用于运行 Nginx 和处理并发请求）

### 1.2 部署模式说明

本项目采用**前后端分离**的部署模式：

- 前端是纯静态资源，由开发团队构建后提供给运维人员
- 部署人员只需将这些静态文件部署到服务器并配置 Nginx
- 服务器不需要安装 Node.js 或其他前端开发环境
- API 请求通过 Nginx 代理转发到后端服务

### 1.3 构建环境说明

> **注意**：以下信息仅供部署人员了解项目构建环境，不需要在服务器上安装这些工具。

本项目由开发团队使用以下环境构建：

- **Node.js 版本**：v18.x LTS 或 v20.x LTS
- **包管理工具**：pnpm
- **构建命令**：
  - 生产环境：`pnpm run build`

## 2. 部署文件说明

### 2.1 静态资源文件

开发团队会提供以下文件用于部署：

- **dist 目录**：包含所有静态资源文件，这是需要部署到服务器的主要内容
- **dist.tar**：打包后的压缩文件，包含 dist 目录的内容，便于传输

### 2.2 文件结构说明

静态资源的目录结构如下：

```
dist/
├── index.html           # 入口 HTML 文件
├── static/              # 静态资源目录
│   ├── js/              # JavaScript 文件
│   │   ├── chunk/       # 代码分块
│   │   └── entry/       # 入口文件
│   ├── css/             # 样式文件
│   ├── fonts/           # 字体文件
│   └── images/          # 图片资源
└── favicon.ico          # 网站图标
```

## 3. 部署流程

### 3.1 接收前端构建产物

从开发团队获取前端构建产物（dist 目录或 dist.tar 压缩包）。

### 3.2 部署到服务器

1. **如果收到的是压缩包**：

   ```bash
   # 登录到服务器
   ssh username@server_ip

   # 进入部署目录
   cd /path/to/deployment/

   # 解压文件
   tar -xf dist.tar
   ```

2. **设置适当的文件权限**：

   ```bash
   # 设置适当的文件权限
   chmod -R 755 dist/
   ```

3. **备份旧版本**（如果是更新部署）：

   ```bash
   # 备份当前版本（如果存在）
   if [ -d "dist_old" ]; then
     rm -rf dist_old_backup
     mv dist_old dist_old_backup
   fi

   if [ -d "dist_current" ]; then
     mv dist_current dist_old
   fi

   # 将新版本设为当前版本
   mv dist dist_current
   ```

## 4. Nginx 配置

### 4.1 Nginx 配置示例

以下是适用于本项目的 Nginx 配置示例，请根据实际环境修改相关配置。所有需要替换的部分都已用【需替换】标注：

```nginx
server {
    listen 80;
    # 【需替换】替换为您的域名或IP
    server_name example.com;

    # 【需替换】网站根目录，替换为实际部署路径
    root /path/to/deployment/dist_current;

    # 开启sendfile优化
    sendfile on;
    tcp_nopush on;
    tcp_nodelay on;

    # 客户端连接相关设置
    keepalive_timeout 65;
    client_max_body_size 10m;

    # 默认首页
    index index.html;

    # 启用 gzip 压缩（提高传输效率）
    gzip on;
    gzip_min_length 1k;          # 最小压缩文件大小
    gzip_comp_level 6;           # 压缩级别，1-9，级别越高压缩率越高，但CPU占用也越高
    gzip_buffers 4 16k;          # 设置用于压缩响应的缓冲区数量和大小
    gzip_http_version 1.1;       # 设置压缩使用的最低HTTP协议版本
    gzip_types text/plain text/css application/json application/javascript application/x-javascript text/xml application/xml application/xml+rss text/javascript image/svg+xml;
    gzip_vary on;                # 增加响应头"Vary: Accept-Encoding"
    gzip_proxied any;            # 对所有类型的代理请求启用压缩
    gzip_disable "MSIE [1-6]\."; # 禁用IE 6 gzip

    # 静态资源缓存设置
    # 不同类型文件设置不同的缓存时间
    location ~* \.(html|htm)$ {
        expires 1h;
        add_header Cache-Control "public";
        # 安全相关头信息
        add_header X-Content-Type-Options nosniff;
        add_header X-XSS-Protection "1; mode=block";
    }

    location ~* \.(css|js)$ {
        expires 7d;
        add_header Cache-Control "public, max-age=604800";
        # 启用Brotli压缩（如果服务器支持）
        brotli_static on;
        # 启用gzip静态压缩文件
        gzip_static on;
        # 安全相关头信息
        add_header X-Content-Type-Options nosniff;
    }

    location ~* \.(jpg|jpeg|png|gif|ico|svg|webp)$ {
        expires 30d;
        add_header Cache-Control "public, max-age=2592000";
        # 图片不需要压缩，但需要缓存
        add_header X-Content-Type-Options nosniff;
        # 禁用不必要的Etag比较
        etag off;
        # 对于图片类资源，可以添加更宽松的跨域设置
        add_header Access-Control-Allow-Origin "*";
    }

    location ~* \.(woff|woff2|ttf|eot)$ {
        expires 30d;
        add_header Cache-Control "public, max-age=2592000";
        # 字体文件跨域设置
        add_header Access-Control-Allow-Origin "*";
        # 安全相关头信息
        add_header X-Content-Type-Options nosniff;
    }

    # 处理前端路由（Hash模式）
    # 本项目使用的是Hash路由模式，如配置文件中的VITE_ROUTER_HISTORY="hash"
    location / {
        try_files $uri $uri/ /index.html;
        # 添加安全相关头信息
        add_header X-Content-Type-Options nosniff;
        add_header X-XSS-Protection "1; mode=block";
        add_header X-Frame-Options "SAMEORIGIN";
        # 添加内容安全策略
        add_header Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data:; font-src 'self' data:; connect-src 'self'";
    }

    # API 代理配置（根据vite.config.ts中的配置）
    # 【需替换】后端服务地址需要根据实际环境修改
    location /rest/portal-server/ {
        # 【需替换】替换为实际的后端服务地址和端口
        proxy_pass http://backend-server:11030/;

        # 基础HTTP请求头设置
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;

        # WebSocket支持
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";

        # 超时设置
        proxy_connect_timeout 60s;
        proxy_read_timeout 60s;
        proxy_send_timeout 60s;
    }

    location /rest/app-server/ {
        # 【需替换】替换为实际的应用服务地址和端口
        proxy_pass http://app-server:9998/;

        # 基础HTTP请求头设置
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;

        # WebSocket支持
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";

        # 超时设置
        proxy_connect_timeout 60s;
        proxy_read_timeout 60s;
        proxy_send_timeout 60s;
    }

    # 通用REST API代理
    location /rest/ {
        # 【需替换】替换为实际的通用API服务地址和端口
        proxy_pass http://api-server:9998/;

        # 基础HTTP请求头设置
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;

        # WebSocket支持
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";

        # 超时设置
        proxy_connect_timeout 60s;
        proxy_read_timeout 60s;
        proxy_send_timeout 60s;
    }

    # 防止访问隐藏文件
    location ~ /\. {
        deny all;
        access_log off;
        log_not_found off;
    }

    # 错误页面配置
    error_page 404 /index.html;

    # 【需替换】日志配置，替换为实际的日志路径
    access_log /var/log/nginx/frontend_access.log combined buffer=16k;
    error_log /var/log/nginx/frontend_error.log warn;

    # 性能优化：禁用不必要的日志记录
    location = /favicon.ico { access_log off; log_not_found off; }
    location = /robots.txt  { access_log off; log_not_found off; }
}
```

## 5. 常见问题及解决方案

### 5.1 白屏问题

**问题**：部署后访问网站出现白屏，控制台可能有资源加载错误。

**可能原因及解决方案**：

1. **路径配置错误**：
   - 检查 Nginx 中的 `root` 路径是否正确指向 `dist_current` 目录
   - 确认 Nginx 配置中的 `server_name` 是否与访问域名一致

2. **资源加载失败**：
   - 检查浏览器控制台错误信息
   - 确认静态资源文件权限是否正确（755）
   - 检查 Nginx 配置中的路径映射

3. **跨域问题**：
   - 检查 Nginx 中的 API 代理配置是否正确
   - 确认后端服务是否正常运行

### 5.2 API 请求失败

**问题**：前端能正常加载，但 API 请求返回 404 或连接失败。

**解决方案**：

1. 检查 Nginx 中的代理配置是否正确
2. 确认后端服务地址和端口是否正确
3. 检查后端服务是否正常运行
4. 检查网络连接和防火墙设置

### 5.3 性能问题

**问题**：页面加载缓慢。

**解决方案**：

1. 确认已启用 Nginx 的 gzip 压缩（参考 Nginx 配置中的 gzip 相关设置）
2. 检查并优化静态资源缓存配置（参考 Nginx 配置中的 expires 和 Cache-Control 设置）
3. 优化服务器配置：
   - 增加服务器内存和CPU资源
   - 确保网络带宽充足
   - 考虑使用更高性能的存储设备

## 6. 更新部署

当需要更新已部署的应用时，请按照以下步骤操作：

1. 从开发团队获取新版本的前端构建产物（dist 目录或 dist.tar）
2. 按照第 3 部分的部署流程进行操作，确保备份当前版本
3. 如有必要，重启 Nginx：`sudo systemctl restart nginx`

## 7. 联系支持

如遇到无法解决的问题，请联系技术支持团队

